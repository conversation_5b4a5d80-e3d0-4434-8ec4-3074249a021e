# Do IT Application - Data Architecture

## 8. Data Architecture

### 8.1 Firestore Database Structure

```json
{
  "users": {
    "userId": {
      "profile": {
        "email": "string",
        "displayName": "string",
        "photoURL": "string",
        "dateOfBirth": "timestamp",
        "gender": "string",
        "height": "number",
        "weight": "number",
        "age": "number",
        "fitnessLevel": "string",
        "goals": ["string"],
        "language": "string",
        "preferences": {
          "workoutDuration": "number",
          "workoutFrequency": "number",
          "dietaryRestrictions": ["string"]
        },
        "createdAt": "timestamp",
        "lastLogin": "timestamp"
      },
      "subscription": {
        "plan": "string", // "basic", "vip", "none"
        "status": "string", // "active", "expired", "cancelled"
        "startDate": "timestamp",
        "endDate": "timestamp",
        "paymentMethod": "string",
        "amount": "number", // 300 for basic, 550 for vip
        "currency": "QAR",
        "autoRenew": "boolean"
      },
      "progress": {
        "currentWeight": "number",
        "bmi": "number",
        "dailyCalories": "number",
        "hydrationGoal": "number",
        "measurements": {
          "chest": "number",
          "waist": "number",
          "hips": "number"
        },
        "photos": ["string"], // AWS S3 URLs
        "progressVideos": ["string"], // AWS S3 URLs
        "lastUpdated": "timestamp"
      },
      "assignedCoach": "string", // coachId for VIP users
      "role": "string" // "user", "coach", "admin"
    }
  },
  "exercises": {
    "exerciseId": {
      "name": "string",
      "description": "string",
      "targetMuscles": ["string"],
      "difficulty": "string",
      "equipment": ["string"],
      "instructions": "string",
      "photoURL": "string", // AWS S3 URL for instructional photo
      "videoURL": "string", // Mux video URL
      "muxAssetId": "string", // Mux asset identifier
      "muxPlaybackId": "string", // Mux playback identifier
      "category": "string", // "strength", "cardio", "flexibility", etc.
      "createdBy": "string",
      "createdAt": "timestamp"
    }
  },
  "workoutPlans": {
    "planId": {
      "title": "string",
      "description": "string",
      "difficulty": "string",
      "duration": "number", // total workout duration in minutes
      "targetMuscles": ["string"],
      "userId": "string", // assigned user
      "exercises": [
        {
          "exerciseId": "string", // reference to exercises collection
          "sets": "number",
          "reps": "number",
          "duration": "number", // exercise duration in seconds
          "restTime": "number", // rest time between sets in seconds
          "weight": "number", // recommended weight
          "order": "number" // exercise order in workout
        }
      ],
      "createdBy": "string", // coach or system generated
      "createdAt": "timestamp",
      "isActive": "boolean"
    }
  },
  "dietPlans": {
    "planId": {
      "title": "string",
      "description": "string",
      "totalCalories": "number",
      "macros": {
        "protein": "number",
        "carbs": "number",
        "fat": "number"
      },
      "meals": [
        {
          "mealType": "string",
          "foods": [
            {
              "name": "string",
              "quantity": "number",
              "unit": "string",
              "calories": "number"
            }
          ]
        }
      ],
      "createdBy": "string",
      "createdAt": "timestamp"
    }
  },
  "coaches": {
    "coachId": {
      "profile": {
        "name": "string",
        "email": "string",
        "photoURL": "string",
        "specializations": ["string"],
        "experience": "number",
        "rating": "number",
        "bio": "string"
      },
      "availability": {
        "schedule": "object",
        "timezone": "string"
      },
      "clients": ["userId"]
    }
  },
  "messages": {
    "conversationId": {
      "participants": ["userId", "coachId"],
      "messages": [
        {
          "senderId": "string",
          "content": "string",
          "timestamp": "timestamp",
          "type": "string",
          "attachments": ["string"]
        }
      ],
      "lastMessage": "timestamp"
    }
  }
}
```

### 8.2 Data Relationships

```mermaid
erDiagram
    USERS ||--o{ WORKOUT_PLANS : assigned
    USERS ||--o{ DIET_PLANS : assigned
    USERS ||--o{ PROGRESS : tracks
    USERS ||--o{ MESSAGES : sends
    COACHES ||--o{ USERS : coaches
    COACHES ||--o{ MESSAGES : sends
    COACHES ||--o{ WORKOUT_PLANS : creates
    COACHES ||--o{ DIET_PLANS : creates
    WORKOUT_PLANS ||--o{ EXERCISES : contains
    EXERCISES ||--o{ VIDEOS : references
```

### 8.3 Data Access Patterns

| Operation | Collection | Index Requirements | Security Rules |
|-----------|------------|-------------------|----------------|
| **User Profile** | users | userId | User can read/write own profile |
| **Workout Plans** | workoutPlans | userId, difficulty, targetMuscles | User can read assigned plans |
| **Progress Tracking** | users/progress | userId, timestamp | User can read/write own progress |
| **Coach Messages** | messages | conversationId, timestamp | Participants can read/write |
| **Admin Analytics** | All collections | Various composite indexes | Admin role required |

### 8.4 Data Storage Strategy

#### 8.4.1 Firestore Collections
- **Primary Data**: User profiles, workout plans, diet plans, exercises
- **Real-time Data**: Chat messages, progress updates, notifications
- **Analytics Data**: User engagement, system metrics, performance data

#### 8.4.2 AWS S3 Storage
- **User Media**: Profile photos, progress photos/videos
- **Content Media**: Exercise instruction photos, supplementary content
- **System Assets**: App icons, branding materials, static content

#### 8.4.3 Mux Video Platform
- **Exercise Videos**: Instructional workout videos with adaptive streaming
- **User Content**: Progress videos (if applicable)
- **Live Streaming**: Future feature for live coaching sessions

### 8.5 Data Migration & Backup Strategy

#### 8.5.1 Backup Procedures
- **Firestore**: Automated daily backups using Firebase Admin SDK
- **AWS S3**: Cross-region replication for media files
- **User Data**: Weekly encrypted exports for compliance

#### 8.5.2 Data Retention Policies
- **Active Users**: Indefinite retention while subscription is active
- **Inactive Users**: 2-year retention after last login
- **Deleted Accounts**: 30-day soft delete, then permanent removal
- **Chat Messages**: 1-year retention for VIP users

---

**Navigation**: [← Back: System Architecture](04-SYSTEM-ARCHITECTURE.MD) | [Back to Main](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: API Specifications →](06-API-SPECIFICATIONS.MD)