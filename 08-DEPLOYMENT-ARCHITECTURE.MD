# Do IT Application - Deployment Architecture

## 11. Deployment Architecture

### 11.1 Environment Strategy

#### 11.1.1 Environment Overview

```mermaid
graph TB
    subgraph "Development Environment"
        A[Local Development]
        B[Firebase Emulator Suite]
        C[Local S3 Mock]
    end
    
    subgraph "Staging Environment"
        D[Firebase Staging Project]
        E[AWS S3 Staging Bucket]
        F[Mux Staging Environment]
    end
    
    subgraph "Production Environment"
        G[Firebase Production Project]
        H[AWS S3 Production Bucket]
        I[Mux Production Environment]
        J[CDN Distribution]
    end
    
    A --> D
    D --> G
    B --> D
    C --> E
    E --> H
    F --> I
```

#### 11.1.2 Development Environment Setup

```yaml
# docker-compose.yml for local development
version: '3.8'
services:
  firebase-emulator:
    image: firebase/emulator-suite
    ports:
      - "4000:4000"  # Emulator UI
      - "9099:9099"  # Authentication
      - "8080:8080"  # Firestore
      - "5001:5001"  # Functions
    volumes:
      - ./firebase.json:/firebase.json
      - ./firestore.rules:/firestore.rules
      - ./functions:/functions
    environment:
      - FIREBASE_PROJECT=do-it-dev
      
  localstack:
    image: localstack/localstack
    ports:
      - "4566:4566"  # LocalStack gateway
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - "./tmp/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
```

#### 11.1.3 Staging Environment Configuration

```javascript
// staging.config.js
const stagingConfig = {
  firebase: {
    projectId: "do-it-staging",
    apiKey: process.env.FIREBASE_STAGING_API_KEY,
    authDomain: "do-it-staging.firebaseapp.com",
    storageBucket: "do-it-staging.appspot.com"
  },
  aws: {
    region: "us-east-1",
    s3Bucket: "do-it-staging-media",
    cloudFrontDistribution: "staging-cdn.do-it.app"
  },
  mux: {
    tokenId: process.env.MUX_STAGING_TOKEN_ID,
    tokenSecret: process.env.MUX_STAGING_TOKEN_SECRET,
    webhookSecret: process.env.MUX_STAGING_WEBHOOK_SECRET
  },
  app: {
    apiUrl: "https://staging-api.do-it.app",
    webUrl: "https://staging.do-it.app"
  }
};
```

#### 11.1.4 Production Environment Configuration

```javascript
// production.config.js
const productionConfig = {
  firebase: {
    projectId: "do-it-production",
    apiKey: process.env.FIREBASE_PROD_API_KEY,
    authDomain: "do-it.app",
    storageBucket: "do-it-production.appspot.com"
  },
  aws: {
    region: "us-east-1",
    s3Bucket: "do-it-production-media",
    cloudFrontDistribution: "cdn.do-it.app"
  },
  mux: {
    tokenId: process.env.MUX_PROD_TOKEN_ID,
    tokenSecret: process.env.MUX_PROD_TOKEN_SECRET,
    webhookSecret: process.env.MUX_PROD_WEBHOOK_SECRET
  },
  app: {
    apiUrl: "https://api.do-it.app",
    webUrl: "https://do-it.app"
  }
};
```

### 11.2 CI/CD Pipeline

#### 11.2.1 GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy Do IT Application

on:
  push:
    branches: [main, staging, develop]
  pull_request:
    branches: [main, staging]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd functions && npm ci
          
      - name: Run tests
        run: |
          npm run test:unit
          npm run test:integration
          
      - name: Run Firebase emulator tests
        run: |
          npm install -g firebase-tools
          firebase emulators:exec --only firestore,auth "npm run test:firestore"
          
  build-mobile:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup React Native
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd mobile
          npm ci
          
      - name: Build Android APK
        run: |
          cd mobile/android
          ./gradlew assembleRelease
          
      - name: Upload APK artifact
        uses: actions/upload-artifact@v3
        with:
          name: android-apk
          path: mobile/android/app/build/outputs/apk/release/
          
  deploy-staging:
    needs: [test, build-mobile]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install Firebase CLI
        run: npm install -g firebase-tools
        
      - name: Deploy to Firebase Staging
        run: |
          firebase use staging
          firebase deploy --only functions,firestore:rules,hosting
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_STAGING_TOKEN }}
          
      - name: Deploy Admin Dashboard to Vercel Staging
        run: |
          cd admin-dashboard
          npx vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_STAGING_PROJECT_ID }}
          
  deploy-production:
    needs: [test, build-mobile]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install Firebase CLI
        run: npm install -g firebase-tools
        
      - name: Deploy to Firebase Production
        run: |
          firebase use production
          firebase deploy --only functions,firestore:rules,hosting
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_PROD_TOKEN }}
          
      - name: Deploy Admin Dashboard to Vercel Production
        run: |
          cd admin-dashboard
          npx vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROD_PROJECT_ID }}
          
      - name: Notify deployment success
        run: |
          curl -X POST ${{ secrets.SLACK_WEBHOOK_URL }} \
            -H 'Content-type: application/json' \
            --data '{"text":"🚀 Do IT Application deployed to production successfully!"}'
```

#### 11.2.2 Mobile App Deployment Pipeline

```yaml
# .github/workflows/mobile-deploy.yml
name: Mobile App Deployment

on:
  push:
    tags:
      - 'v*'

jobs:
  deploy-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Xcode
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: latest-stable
          
      - name: Install dependencies
        run: |
          cd mobile
          npm ci
          cd ios && pod install
          
      - name: Build iOS app
        run: |
          cd mobile/ios
          xcodebuild -workspace DoIT.xcworkspace \
            -scheme DoIT \
            -configuration Release \
            -archivePath DoIT.xcarchive \
            archive
            
      - name: Upload to App Store Connect
        run: |
          cd mobile/ios
          xcodebuild -exportArchive \
            -archivePath DoIT.xcarchive \
            -exportPath . \
            -exportOptionsPlist ExportOptions.plist
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          
  deploy-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'temurin'
          
      - name: Setup Android SDK
        uses: android-actions/setup-android@v2
        
      - name: Install dependencies
        run: |
          cd mobile
          npm ci
          
      - name: Build Android AAB
        run: |
          cd mobile/android
          ./gradlew bundleRelease
          
      - name: Sign AAB
        run: |
          cd mobile/android
          jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 \
            -keystore ${{ secrets.ANDROID_KEYSTORE }} \
            -storepass ${{ secrets.KEYSTORE_PASSWORD }} \
            app/build/outputs/bundle/release/app-release.aab \
            ${{ secrets.KEY_ALIAS }}
            
      - name: Upload to Google Play Console
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.doit.app
          releaseFiles: mobile/android/app/build/outputs/bundle/release/app-release.aab
          track: production
```

### 11.3 Infrastructure Requirements

#### 11.3.1 Firebase Configuration

```javascript
// firebase.json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18",
    "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"],
    "ignore": [
      "node_modules",
      ".git",
      "firebase-debug.log",
      "firebase-debug.*.log"
    ]
  },
  "hosting": {
    "public": "public",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=********"
          }
        ]
      }
    ]
  },
  "storage": {
    "rules": "storage.rules"
  },
  "emulators": {
    "auth": {
      "port": 9099
    },
    "functions": {
      "port": 5001
    },
    "firestore": {
      "port": 8080
    },
    "hosting": {
      "port": 5000
    },
    "ui": {
      "enabled": true,
      "port": 4000
    }
  }
}
```

#### 11.3.2 AWS S3 Configuration

```yaml
# terraform/s3.tf
resource "aws_s3_bucket" "media_storage" {
  bucket = "do-it-${var.environment}-media"
  
  tags = {
    Name        = "Do IT Media Storage"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_versioning" "media_versioning" {
  bucket = aws_s3_bucket.media_storage.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_encryption" "media_encryption" {
  bucket = aws_s3_bucket.media_storage.id

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "aws_s3_bucket_cors_configuration" "media_cors" {
  bucket = aws_s3_bucket.media_storage.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE"]
    allowed_origins = ["https://do-it.app", "https://admin.do-it.app"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_cloudfront_distribution" "media_cdn" {
  origin {
    domain_name = aws_s3_bucket.media_storage.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.media_storage.bucket}"

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.media_oai.cloudfront_access_identity_path
    }
  }

  enabled = true
  comment = "Do IT Media CDN"

  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "S3-${aws_s3_bucket.media_storage.bucket}"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    min_ttl     = 0
    default_ttl = 3600
    max_ttl     = 86400
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  tags = {
    Name        = "Do IT Media CDN"
    Environment = var.environment
  }
}
```

### 11.4 Monitoring & Logging

#### 11.4.1 Application Monitoring Setup

```javascript
// monitoring/setup.js
import { initializeApp } from 'firebase/app';
import { getPerformance } from 'firebase/performance';
import { getAnalytics } from 'firebase/analytics';
import * as Sentry from '@sentry/react-native';

// Initialize Firebase Performance Monitoring
const app = initializeApp(firebaseConfig);
const perf = getPerformance(app);
const analytics = getAnalytics(app);

// Initialize Sentry for error tracking
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Console(),
  ],
});

// Custom performance monitoring
export const trackCustomMetric = (metricName, value, attributes = {}) => {
  const customTrace = perf.trace(metricName);
  customTrace.start();
  
  // Add custom attributes
  Object.entries(attributes).forEach(([key, val]) => {
    customTrace.putAttribute(key, val);
  });
  
  customTrace.putMetric(metricName, value);
  customTrace.stop();
};

// Error boundary for React components
export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack,
        },
      },
    });
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallbackComponent />;
    }

    return this.props.children;
  }
}
```

#### 11.4.2 Infrastructure Monitoring

```yaml
# monitoring/alerts.yml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: do-it-alerts
spec:
  groups:
  - name: do-it.rules
    rules:
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "High error rate detected"
        description: "Error rate is above 10% for 5 minutes"
        
    - alert: HighResponseTime
      expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High response time detected"
        description: "95th percentile response time is above 1 second"
        
    - alert: DatabaseConnectionFailure
      expr: firebase_database_connections_failed_total > 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Database connection failures"
        description: "Firebase database connection failures detected"
```

### 11.5 Backup & Disaster Recovery

#### 11.5.1 Automated Backup Strategy

```javascript
// functions/backup.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { Storage } = require('@google-cloud/storage');

const storage = new Storage();
const bucket = storage.bucket('do-it-backups');

// Daily Firestore backup
exports.dailyBackup = functions.pubsub.schedule('0 2 * * *').onRun(async (context) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const backupName = `firestore-backup-${timestamp}`;
  
  try {
    // Export Firestore data
    const operation = await admin.firestore().exportDocuments({
      outputUriPrefix: `gs://do-it-backups/firestore/${backupName}`,
      collectionIds: ['users', 'exercises', 'workoutPlans', 'dietPlans', 'chats', 'progress']
    });
    
    functions.logger.info(`Backup operation started: ${operation.name}`);
    
    // Store backup metadata
    await admin.firestore().collection('backups').add({
      name: backupName,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      status: 'in_progress',
      operationName: operation.name
    });
    
    return { success: true, operationName: operation.name };
  } catch (error) {
    functions.logger.error('Backup failed:', error);
    throw new functions.https.HttpsError('internal', 'Backup operation failed');
  }
});

// Weekly S3 backup verification
exports.weeklyBackupVerification = functions.pubsub.schedule('0 3 * * 0').onRun(async (context) => {
  try {
    const [files] = await bucket.getFiles({ prefix: 'firestore/' });
    const recentBackups = files.filter(file => {
      const fileDate = new Date(file.metadata.timeCreated);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return fileDate > weekAgo;
    });
    
    if (recentBackups.length < 7) {
      functions.logger.error(`Only ${recentBackups.length} backups found in the last week`);
      // Send alert to admin team
      await sendBackupAlert('Missing backups detected');
    }
    
    functions.logger.info(`Backup verification complete: ${recentBackups.length} backups verified`);
  } catch (error) {
    functions.logger.error('Backup verification failed:', error);
  }
});
```

#### 11.5.2 Disaster Recovery Plan

```javascript
// disaster-recovery/restore.js
class DisasterRecovery {
  constructor() {
    this.storage = new Storage();
    this.backupBucket = this.storage.bucket('do-it-backups');
  }

  // Restore from latest backup
  async restoreFromLatestBackup() {
    try {
      // Find latest backup
      const [files] = await this.backupBucket.getFiles({ 
        prefix: 'firestore/',
        orderBy: 'timeCreated',
        order: 'desc'
      });
      
      if (files.length === 0) {
        throw new Error('No backups found');
      }
      
      const latestBackup = files[0];
      const backupPath = `gs://do-it-backups/${latestBackup.name}`;
      
      // Import data to new Firestore instance
      const operation = await admin.firestore().importDocuments({
        inputUriPrefix: backupPath
      });
      
      console.log(`Restore operation started: ${operation.name}`);
      return { success: true, operationName: operation.name };
    } catch (error) {
      console.error('Restore failed:', error);
      throw error;
    }
  }

  // Point-in-time recovery
  async restoreToPointInTime(targetDate) {
    try {
      const backupName = `firestore-backup-${targetDate}`;
      const backupPath = `gs://do-it-backups/firestore/${backupName}`;
      
      const operation = await admin.firestore().importDocuments({
        inputUriPrefix: backupPath
      });
      
      console.log(`Point-in-time restore started: ${operation.name}`);
      return { success: true, operationName: operation.name };
    } catch (error) {
      console.error('Point-in-time restore failed:', error);
      throw error;
    }
  }

  // Validate restored data integrity
  async validateDataIntegrity() {
    try {
      const collections = ['users', 'exercises', 'workoutPlans', 'dietPlans'];
      const validationResults = {};
      
      for (const collection of collections) {
        const snapshot = await admin.firestore().collection(collection).count().get();
        validationResults[collection] = snapshot.data().count;
      }
      
      console.log('Data integrity validation:', validationResults);
      return validationResults;
    } catch (error) {
      console.error('Data validation failed:', error);
      throw error;
    }
  }
}
```

### 11.6 Performance Optimization

#### 11.6.1 Database Optimization

```javascript
// firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "subscription.plan", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "workoutPlans",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "exercises",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "category", "order": "ASCENDING" },
        { "fieldPath": "difficulty", "order": "ASCENDING" },
        { "fieldPath": "rating", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "messages",
      "queryScope": "COLLECTION_GROUP",
      "fields": [
        { "fieldPath": "chatId", "order": "ASCENDING" },
        { "fieldPath": "timestamp", "order": "ASCENDING" }
      ]
    }
  ],
  "fieldOverrides": [
    {
      "collectionGroup": "users",
      "fieldPath": "email",
      "indexes": [
        { "order": "ASCENDING", "queryScope": "COLLECTION" }
      ]
    }
  ]
}
```

#### 11.6.2 CDN and Caching Strategy

```javascript
// cdn/cache-config.js
const cacheConfig = {
  // Static assets - long cache
  staticAssets: {
    pattern: /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/,
    headers: {
      'Cache-Control': 'public, max-age=********, immutable',
      'Expires': new Date(Date.now() + ********000).toUTCString()
    }
  },
  
  // API responses - short cache with revalidation
  apiResponses: {
    pattern: /^\/api\//,
    headers: {
      'Cache-Control': 'public, max-age=300, stale-while-revalidate=60',
      'Vary': 'Authorization'
    }
  },
  
  // Video content - adaptive caching
  videoContent: {
    pattern: /\.(mp4|m3u8|ts)$/,
    headers: {
      'Cache-Control': 'public, max-age=86400',
      'Access-Control-Allow-Origin': '*'
    }
  },
  
  // User-specific content - no cache
  userContent: {
    pattern: /^\/dashboard|\/profile/,
    headers: {
      'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  }
};
```

---

**Navigation**: [← Back: Security Architecture](07-SECURITY-ARCHITECTURE.MD) | [Back to Main](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: Implementation Roadmap →](09-IMPLEMENTATION-ROADMAP.MD)