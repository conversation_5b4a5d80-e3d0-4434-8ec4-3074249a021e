# Enhanced Security Architecture

## Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Data Protection & Privacy](#data-protection--privacy)
4. [Network Security](#network-security)
5. [Application Security](#application-security)
6. [Infrastructure Security](#infrastructure-security)
7. [Compliance & Governance](#compliance--governance)
8. [Incident Response](#incident-response)
9. [Security Monitoring](#security-monitoring)
10. [Security Testing](#security-testing)

---

## 1. Security Overview

### 1.1 Security Philosophy
The Do IT application implements a **defense-in-depth** security strategy with multiple layers of protection to ensure user data privacy, system integrity, and service availability.

### 1.2 Security Principles
- **Zero Trust Architecture**: Never trust, always verify
- **Principle of Least Privilege**: Minimal access rights for users and systems
- **Data Minimization**: Collect and store only necessary data
- **Privacy by Design**: Security and privacy built into every component
- **Continuous Monitoring**: Real-time threat detection and response

### 1.3 Threat Model

```mermaid
graph TB
    subgraph "External Threats"
        A[Malicious Actors]
        B[Data Breaches]
        C[DDoS Attacks]
        D[API Abuse]
    end
    
    subgraph "Internal Threats"
        E[Insider Threats]
        F[Misconfigurations]
        G[Privilege Escalation]
        H[Data Leakage]
    end
    
    subgraph "Technical Threats"
        I[Code Vulnerabilities]
        J[Dependency Exploits]
        K[Infrastructure Flaws]
        L[Third-party Risks]
    end
    
    subgraph "Mitigation Strategies"
        M[Authentication]
        N[Authorization]
        O[Encryption]
        P[Monitoring]
        Q[Incident Response]
    end
    
    A --> M
    B --> O
    C --> P
    D --> N
    E --> N
    F --> P
    G --> N
    H --> O
    I --> M
    J --> P
    K --> O
    L --> Q
```

---

## 2. Authentication & Authorization

### 2.1 Multi-Factor Authentication

#### 2.1.1 Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant FA as Firebase Auth
    participant SMS as SMS Provider
    participant E as Email Service
    
    U->>A: Login Request
    A->>FA: Verify Credentials
    FA->>A: Primary Auth Success
    A->>U: Request MFA Method
    U->>A: Select SMS/Email
    A->>SMS: Send OTP
    SMS->>U: Receive OTP
    U->>A: Enter OTP
    A->>FA: Verify OTP
    FA->>A: MFA Success
    A->>U: Access Granted
```

#### 2.1.2 MFA Implementation
```javascript
// Enable MFA for user account
const enableMFA = async (userId, method) => {
  try {
    const user = await admin.auth().getUser(userId);
    
    // Configure MFA settings
    const mfaConfig = {
      factorId: method === 'sms' ? 'phone' : 'email',
      displayName: `${method.toUpperCase()} Verification`,
      enrollmentTime: new Date().toISOString()
    };
    
    // Update user custom claims
    await admin.auth().setCustomUserClaims(userId, {
      mfaEnabled: true,
      mfaMethod: method,
      securityLevel: 'high'
    });
    
    return { success: true, mfaEnabled: true };
  } catch (error) {
    console.error('Error enabling MFA:', error);
    throw error;
  }
};

// Verify MFA token
const verifyMFAToken = async (userId, token, method) => {
  try {
    let isValid = false;
    
    if (method === 'sms') {
      isValid = await verifySMSToken(userId, token);
    } else if (method === 'email') {
      isValid = await verifyEmailToken(userId, token);
    }
    
    if (isValid) {
      // Update last MFA verification
      await admin.firestore().collection('users').doc(userId).update({
        lastMFAVerification: admin.firestore.FieldValue.serverTimestamp(),
        mfaAttempts: 0
      });
      
      return { success: true, verified: true };
    } else {
      // Increment failed attempts
      await admin.firestore().collection('users').doc(userId).update({
        mfaAttempts: admin.firestore.FieldValue.increment(1)
      });
      
      return { success: false, verified: false };
    }
  } catch (error) {
    console.error('Error verifying MFA token:', error);
    throw error;
  }
};
```

### 2.2 Role-Based Access Control (RBAC)

#### 2.2.1 Role Hierarchy
```javascript
const ROLES = {
  SUPER_ADMIN: {
    level: 100,
    permissions: ['*'], // All permissions
    description: 'Full system access'
  },
  ADMIN: {
    level: 80,
    permissions: [
      'users.read', 'users.write', 'users.delete',
      'exercises.read', 'exercises.write', 'exercises.delete',
      'plans.read', 'plans.write', 'plans.delete',
      'analytics.read', 'settings.write'
    ],
    description: 'Administrative access'
  },
  COACH: {
    level: 60,
    permissions: [
      'users.read', 'users.update',
      'exercises.read', 'exercises.create',
      'plans.read', 'plans.create', 'plans.update',
      'chats.read', 'chats.write',
      'progress.read'
    ],
    description: 'Coach access to assigned users'
  },
  VIP_USER: {
    level: 40,
    permissions: [
      'profile.read', 'profile.update',
      'plans.read', 'progress.read', 'progress.write',
      'chats.read', 'chats.write',
      'exercises.read'
    ],
    description: 'VIP user with coach access'
  },
  BASIC_USER: {
    level: 20,
    permissions: [
      'profile.read', 'profile.update',
      'plans.read', 'progress.read', 'progress.write',
      'exercises.read'
    ],
    description: 'Basic user access'
  },
  FREE_USER: {
    level: 10,
    permissions: [
      'profile.read', 'profile.update',
      'exercises.read'
    ],
    description: 'Limited free user access'
  }
};
```

#### 2.2.2 Permission Validation
```javascript
// Permission validation middleware
const checkPermission = (requiredPermission) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.uid;
      const userDoc = await admin.firestore()
        .collection('users')
        .doc(userId)
        .get();
      
      const userData = userDoc.data();
      const userRole = userData?.role || 'FREE_USER';
      const roleConfig = ROLES[userRole];
      
      if (!roleConfig) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INVALID_ROLE',
            message: 'User has invalid role assignment'
          }
        });
      }
      
      // Check if user has required permission
      const hasPermission = roleConfig.permissions.includes('*') ||
                           roleConfig.permissions.includes(requiredPermission);
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: `Permission '${requiredPermission}' required`,
            userRole: userRole,
            requiredPermission: requiredPermission
          }
        });
      }
      
      req.userRole = userRole;
      req.permissions = roleConfig.permissions;
      next();
    } catch (error) {
      console.error('Error checking permissions:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'PERMISSION_CHECK_FAILED',
          message: 'Failed to validate user permissions'
        }
      });
    }
  };
};
```

### 2.3 Session Management

#### 2.3.1 Secure Session Configuration
```javascript
// Session security configuration
const sessionConfig = {
  // Token expiration times
  accessTokenExpiry: 3600, // 1 hour
  refreshTokenExpiry: 604800, // 7 days
  
  // Session security
  httpOnly: true,
  secure: true, // HTTPS only
  sameSite: 'strict',
  
  // Session rotation
  rotateTokens: true,
  maxConcurrentSessions: 5,
  
  // Inactivity timeout
  inactivityTimeout: 1800, // 30 minutes
  
  // Device tracking
  trackDevices: true,
  maxDevicesPerUser: 10
};

// Session validation
const validateSession = async (token) => {
  try {
    // Verify token signature and expiration
    const decodedToken = await admin.auth().verifyIdToken(token, true);
    
    // Check if session is still active
    const sessionDoc = await admin.firestore()
      .collection('userSessions')
      .doc(decodedToken.uid)
      .collection('activeSessions')
      .doc(decodedToken.jti)
      .get();
    
    if (!sessionDoc.exists) {
      throw new Error('Session not found or expired');
    }
    
    const sessionData = sessionDoc.data();
    
    // Check for suspicious activity
    if (sessionData.lastActivity < Date.now() - sessionConfig.inactivityTimeout * 1000) {
      // Invalidate inactive session
      await sessionDoc.ref.delete();
      throw new Error('Session expired due to inactivity');
    }
    
    // Update last activity
    await sessionDoc.ref.update({
      lastActivity: admin.firestore.FieldValue.serverTimestamp(),
      requestCount: admin.firestore.FieldValue.increment(1)
    });
    
    return {
      valid: true,
      userId: decodedToken.uid,
      sessionId: decodedToken.jti,
      role: decodedToken.role || 'FREE_USER'
    };
  } catch (error) {
    console.error('Session validation failed:', error);
    return {
      valid: false,
      error: error.message
    };
  }
};
```

---

## 3. Data Protection & Privacy

### 3.1 Data Encryption

#### 3.1.1 Encryption at Rest
```javascript
// Field-level encryption for sensitive data
const crypto = require('crypto');

class DataEncryption {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
  }
  
  // Encrypt sensitive field
  encryptField(data, key) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, key, iv);
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encrypted: encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }
  
  // Decrypt sensitive field
  decryptField(encryptedData, key) {
    try {
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const tag = Buffer.from(encryptedData.tag, 'hex');
      
      const decipher = crypto.createDecipher(this.algorithm, key, iv);
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }
}

// Usage example for storing sensitive user data
const encryptUserData = async (userId, sensitiveData) => {
  const encryption = new DataEncryption();
  const userKey = await getUserEncryptionKey(userId);
  
  const encryptedData = {
    personalInfo: encryption.encryptField(sensitiveData.personalInfo, userKey),
    medicalInfo: encryption.encryptField(sensitiveData.medicalInfo, userKey),
    paymentInfo: encryption.encryptField(sensitiveData.paymentInfo, userKey)
  };
  
  return encryptedData;
};
```

#### 3.1.2 Encryption in Transit
```javascript
// TLS configuration for all communications
const tlsConfig = {
  // Minimum TLS version
  minVersion: 'TLSv1.3',
  
  // Cipher suites (strongest first)
  ciphers: [
    'TLS_AES_256_GCM_SHA384',
    'TLS_CHACHA20_POLY1305_SHA256',
    'TLS_AES_128_GCM_SHA256'
  ],
  
  // Certificate validation
  rejectUnauthorized: true,
  checkServerIdentity: true,
  
  // HSTS configuration
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  
  // Certificate pinning
  certificatePinning: {
    enabled: true,
    pins: [
      'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=',
      'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB='
    ]
  }
};
```

### 3.2 Data Privacy Controls

#### 3.2.1 GDPR Compliance
```javascript
// GDPR data subject rights implementation
class GDPRCompliance {
  
  // Right to Access (Article 15)
  async exportUserData(userId) {
    try {
      const userData = await this.collectAllUserData(userId);
      
      const exportData = {
        personalData: userData.profile,
        workoutData: userData.workouts,
        progressData: userData.progress,
        communicationData: userData.messages,
        metadata: {
          exportDate: new Date().toISOString(),
          dataRetentionPeriod: '7 years',
          legalBasis: 'consent'
        }
      };
      
      // Log data export request
      await this.logDataProcessingActivity(userId, 'data_export', {
        requestDate: new Date().toISOString(),
        dataTypes: Object.keys(exportData),
        requestMethod: 'user_portal'
      });
      
      return exportData;
    } catch (error) {
      console.error('Data export failed:', error);
      throw error;
    }
  }
  
  // Right to Erasure (Article 17)
  async deleteUserData(userId, retainLegal = false) {
    const batch = admin.firestore().batch();
    
    try {
      if (retainLegal) {
        // Anonymize data instead of deletion for legal compliance
        await this.anonymizeUserData(userId, batch);
      } else {
        // Complete data deletion
        await this.completeDataDeletion(userId, batch);
      }
      
      await batch.commit();
      
      // Log deletion activity
      await this.logDataProcessingActivity(userId, 'data_deletion', {
        deletionDate: new Date().toISOString(),
        retainLegal: retainLegal,
        deletionMethod: 'user_request'
      });
      
      return { success: true, deleted: true };
    } catch (error) {
      console.error('Data deletion failed:', error);
      throw error;
    }
  }
  
  // Right to Rectification (Article 16)
  async updateUserData(userId, corrections) {
    try {
      const validatedCorrections = await this.validateDataCorrections(corrections);
      
      await admin.firestore().collection('users').doc(userId).update({
        ...validatedCorrections,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        dataVersion: admin.firestore.FieldValue.increment(1)
      });
      
      // Log data correction
      await this.logDataProcessingActivity(userId, 'data_correction', {
        correctionDate: new Date().toISOString(),
        fieldsUpdated: Object.keys(validatedCorrections),
        previousValues: await this.getPreviousValues(userId, Object.keys(validatedCorrections))
      });
      
      return { success: true, updated: true };
    } catch (error) {
      console.error('Data correction failed:', error);
      throw error;
    }
  }
  
  // Data Portability (Article 20)
  async generatePortableData(userId, format = 'json') {
    try {
      const userData = await this.exportUserData(userId);
      
      let portableData;
      switch (format) {
        case 'json':
          portableData = JSON.stringify(userData, null, 2);
          break;
        case 'csv':
          portableData = await this.convertToCSV(userData);
          break;
        case 'xml':
          portableData = await this.convertToXML(userData);
          break;
        default:
          throw new Error('Unsupported export format');
      }
      
      return {
        data: portableData,
        format: format,
        size: Buffer.byteLength(portableData, 'utf8'),
        checksum: crypto.createHash('sha256').update(portableData).digest('hex')
      };
    } catch (error) {
      console.error('Data portability failed:', error);
      throw error;
    }
  }
}
```

### 3.3 Data Loss Prevention (DLP)

#### 3.3.1 Sensitive Data Detection
```javascript
// Sensitive data patterns
const SENSITIVE_PATTERNS = {
  creditCard: /\b(?:\d{4}[-\s]?){3}\d{4}\b/g,
  ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  phone: /\b\+?[\d\s\-\(\)]{10,}\b/g,
  passport: /\b[A-Z]{1,2}\d{6,9}\b/g
};

// DLP scanning function
const scanForSensitiveData = (content) => {
  const findings = [];
  
  for (const [type, pattern] of Object.entries(SENSITIVE_PATTERNS)) {
    const matches = content.match(pattern);
    if (matches) {
      findings.push({
        type: type,
        count: matches.length,
        matches: matches.map(match => ({
          value: match.replace(/./g, '*'), // Mask the value
          position: content.indexOf(match)
        }))
      });
    }
  }
  
  return {
    hasSensitiveData: findings.length > 0,
    findings: findings,
    riskLevel: calculateRiskLevel(findings)
  };
};

// Risk level calculation
const calculateRiskLevel = (findings) => {
  const riskScores = {
    creditCard: 10,
    ssn: 10,
    passport: 8,
    email: 3,
    phone: 2
  };
  
  const totalScore = findings.reduce((sum, finding) => {
    return sum + (riskScores[finding.type] * finding.count);
  }, 0);
  
  if (totalScore >= 20) return 'HIGH';
  if (totalScore >= 10) return 'MEDIUM';
  if (totalScore >= 5) return 'LOW';
  return 'MINIMAL';
};
```

---

## 4. Network Security

### 4.1 API Security

#### 4.1.1 Rate Limiting
```javascript
// Advanced rate limiting with multiple strategies
class RateLimiter {
  constructor() {
    this.limits = {
      // Per-endpoint limits
      '/api/auth/login': { requests: 5, window: 60000, burst: 10 },
      '/api/auth/register': { requests: 3, window: 60000, burst: 5 },
      '/api/user/profile': { requests: 30, window: 60000, burst: 50 },
      '/api/workouts': { requests: 100, window: 60000, burst: 150 },
      '/api/messages': { requests: 200, window: 60000, burst: 300 },
      
      // Global limits
      'global': { requests: 1000, window: 60000, burst: 1500 }
    };
    
    this.storage = new Map(); // In production, use Redis
  }
  
  async checkLimit(identifier, endpoint, req) {
    const now = Date.now();
    const limit = this.limits[endpoint] || this.limits['global'];
    
    // Create unique key for user + endpoint
    const key = `${identifier}:${endpoint}`;
    const globalKey = `${identifier}:global`;
    
    // Check endpoint-specific limit
    const endpointAllowed = await this.checkSpecificLimit(key, limit, now);
    
    // Check global limit
    const globalAllowed = await this.checkSpecificLimit(globalKey, this.limits['global'], now);
    
    if (!endpointAllowed || !globalAllowed) {
      // Log rate limit violation
      console.warn(`Rate limit exceeded for ${identifier} on ${endpoint}`);
      
      // Implement progressive penalties
      await this.applyPenalty(identifier, endpoint);
      
      return {
        allowed: false,
        retryAfter: limit.window / 1000,
        limit: limit.requests,
        remaining: 0
      };
    }
    
    return {
      allowed: true,
      limit: limit.requests,
      remaining: await this.getRemainingRequests(key, limit, now)
    };
  }
  
  async checkSpecificLimit(key, limit, now) {
    const record = this.storage.get(key) || { count: 0, resetTime: now + limit.window };
    
    // Reset if window has passed
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + limit.window;
    }
    
    // Check if limit exceeded
    if (record.count >= limit.requests) {
      return false;
    }
    
    // Increment counter
    record.count++;
    this.storage.set(key, record);
    
    return true;
  }
}
```

#### 4.1.2 Input Validation & Sanitization
```javascript
// Comprehensive input validation
const inputValidator = {
  // Sanitize string input
  sanitizeString: (input, maxLength = 1000) => {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }
    
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/[<>]/g, ''); // Remove angle brackets
  },
  
  // Validate email
  validateEmail: (email) => {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }
    
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /\+.*\+/, // Multiple plus signs
      /\.{2,}/, // Multiple consecutive dots
      /@.*@/, // Multiple @ symbols
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(email)) {
        throw new Error('Email contains suspicious patterns');
      }
    }
    
    return email.toLowerCase();
  },
  
  // Validate and sanitize workout data
  validateWorkoutData: (data) => {
    const schema = {
      exerciseId: { type: 'string', required: true, maxLength: 50 },
      sets: { type: 'number', required: true, min: 1, max: 20 },
      reps: { type: 'number', required: true, min: 1, max: 1000 },
      weight: { type: 'number', required: false, min: 0, max: 1000 },
      duration: { type: 'number', required: false, min: 0, max: 7200 },
      notes: { type: 'string', required: false, maxLength: 500 }
    };
    
    const validated = {};
    
    for (const [field, rules] of Object.entries(schema)) {
      const value = data[field];
      
      // Check required fields
      if (rules.required && (value === undefined || value === null)) {
        throw new Error(`Field '${field}' is required`);
      }
      
      if (value !== undefined && value !== null) {
        // Type validation
        if (rules.type === 'string' && typeof value !== 'string') {
          throw new Error(`Field '${field}' must be a string`);
        }
        
        if (rules.type === 'number' && typeof value !== 'number') {
          throw new Error(`Field '${field}' must be a number`);
        }
        
        // Range validation
        if (rules.type === 'number') {
          if (rules.min !== undefined && value < rules.min) {
            throw new Error(`Field '${field}' must be at least ${rules.min}`);
          }
          if (rules.max !== undefined && value > rules.max) {
            throw new Error(`Field '${field}' must be at most ${rules.max}`);
          }
        }
        
        // String length validation
        if (rules.type === 'string') {
          if (rules.maxLength && value.length > rules.maxLength) {
            throw new Error(`Field '${field}' must be at most ${rules.maxLength} characters`);
          }
          validated[field] = this.sanitizeString(value, rules.maxLength);
        } else {
          validated[field] = value;
        }
      }
    }
    
    return validated;
  }
};
```

### 4.2 DDoS Protection

#### 4.2.1 Traffic Analysis
```javascript
// DDoS detection and mitigation
class DDoSProtection {
  constructor() {
    this.thresholds = {
      requestsPerSecond: 100,
      requestsPerMinute: 1000,
      uniqueIPs: 50,
      suspiciousPatterns: 10
    };
    
    this.monitoring = {
      requests: new Map(),
      ips: new Set(),
      patterns: new Map()
    };
  }
  
  analyzeTraffic(req) {
    const now = Date.now();
    const ip = this.getClientIP(req);
    const userAgent = req.headers['user-agent'] || '';
    const endpoint = req.path;
    
    // Track request patterns
    this.trackRequest(ip, endpoint, now);
    
    // Analyze for suspicious patterns
    const suspiciousScore = this.calculateSuspiciousScore(ip, userAgent, endpoint);
    
    // Check thresholds
    const threats = this.checkThresholds(ip, now);
    
    return {
      ip: ip,
      suspiciousScore: suspiciousScore,
      threats: threats,
      action: this.determineAction(suspiciousScore, threats)
    };
  }
  
  calculateSuspiciousScore(ip, userAgent, endpoint) {
    let score = 0;
    
    // Check for bot patterns
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i
    ];
    
    if (botPatterns.some(pattern => pattern.test(userAgent))) {
      score += 5;
    }
    
    // Check for missing or suspicious user agent
    if (!userAgent || userAgent.length < 10) {
      score += 3;
    }
    
    // Check for rapid sequential requests
    const recentRequests = this.getRecentRequests(ip, 10000); // Last 10 seconds
    if (recentRequests > 50) {
      score += 10;
    }
    
    // Check for suspicious endpoints
    const sensitiveEndpoints = ['/admin', '/api/auth', '/api/user'];
    if (sensitiveEndpoints.some(ep => endpoint.startsWith(ep))) {
      score += 2;
    }
    
    return score;
  }
  
  determineAction(suspiciousScore, threats) {
    if (suspiciousScore >= 15 || threats.length >= 3) {
      return 'BLOCK';
    } else if (suspiciousScore >= 10 || threats.length >= 2) {
      return 'CHALLENGE'; // CAPTCHA or additional verification
    } else if (suspiciousScore >= 5 || threats.length >= 1) {
      return 'THROTTLE'; // Reduce rate limits
    }
    
    return 'ALLOW';
  }
}
```

---

## 5. Application Security

### 5.1 Secure Coding Practices

#### 5.1.1 SQL Injection Prevention
```javascript
// Parameterized queries for Firestore
const secureQuery = {
  // Safe user data retrieval
  getUserByEmail: async (email) => {
    // Input validation
    if (!email || typeof email !== 'string') {
      throw new Error('Invalid email parameter');
    }
    
    // Sanitize input
    const sanitizedEmail = email.toLowerCase().trim();
    
    // Use parameterized query
    const userQuery = admin.firestore()
      .collection('users')
      .where('email', '==', sanitizedEmail)
      .limit(1);
    
    const snapshot = await userQuery.get();
    
    if (snapshot.empty) {
      return null;
    }
    
    return snapshot.docs[0].data();
  },
  
  // Safe workout plan search
  searchWorkoutPlans: async (userId, filters) => {
    // Validate user ID
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid user ID');
    }
    
    // Build safe query
    let query = admin.firestore()
      .collection('workoutPlans')
      .where('userId', '==', userId);
    
    // Apply safe filters
    if (filters.category && typeof filters.category === 'string') {
      const allowedCategories = ['strength', 'cardio', 'flexibility', 'mixed'];
      if (allowedCategories.includes(filters.category)) {
        query = query.where('category', '==', filters.category);
      }
    }
    
    if (filters.difficulty && typeof filters.difficulty === 'string') {
      const allowedDifficulties = ['beginner', 'intermediate', 'advanced'];
      if (allowedDifficulties.includes(filters.difficulty)) {
        query = query.where('difficultyLevel', '==', filters.difficulty);
      }
    }
    
    // Apply pagination safely
    const limit = Math.min(Math.max(1, parseInt(filters.limit) || 10), 100);
    query = query.limit(limit);
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
};
```

#### 5.1.2 Cross-Site Scripting (XSS) Prevention
```javascript
// XSS prevention utilities
const xssProtection = {
  // HTML encoding
  encodeHTML: (str) => {
    const htmlEntities = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;'
    };
    
    return str.replace(/[&<>"'/]/g, (match) => htmlEntities[match]);
  },
  
  // Content Security Policy headers
  setCSPHeaders: (res) => {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "media-src 'self' https://stream.mux.com",
      "connect-src 'self' https://api.do-it-fitness.com wss:",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
    
    res.setHeader('Content-Security-Policy', cspDirectives);
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
  },
  
  // Sanitize user-generated content
  sanitizeUserContent: (content) => {
    // Remove dangerous HTML tags
    const dangerousTags = /<(script|iframe|object|embed|form|input|button|link|meta|style)[^>]*>.*?<\/\1>/gi;
    content = content.replace(dangerousTags, '');
    
    // Remove event handlers
    content = content.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');
    
    // Remove javascript: URLs
    content = content.replace(/javascript:/gi, '');
    
    // Encode remaining HTML
    return this.encodeHTML(content);
  }
};
```

---

**Navigation**: [← Back: API Specifications](COMPREHENSIVE-API-SPECIFICATION.MD) | [Next: Technical Implementation Guidelines →](TECHNICAL-IMPLEMENTATION-GUIDELINES.MD)
