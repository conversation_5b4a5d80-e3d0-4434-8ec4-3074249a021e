# Do IT Application - Software Architecture Document

## Table of Contents

1. [Executive Summary](#1-executive-summary)
2. [System Overview](#2-system-overview)
3. [Architecture Principles](#3-architecture-principles)
4. [Technology Stack](#4-technology-stack)
5. [System Context Diagram](#5-system-context-diagram)
6. [Container Architecture](#6-container-architecture)
7. [Component Architecture](#7-component-architecture)
8. [Data Architecture](#8-data-architecture)
9. [API Specifications](#9-api-specifications)
10. [Security Architecture](#10-security-architecture)
11. [Deployment Architecture](#11-deployment-architecture)
12. [Performance & Scalability](#12-performance--scalability)
13. [Development Guidelines](#13-development-guidelines)
14. [Risk Assessment](#14-risk-assessment)
15. [Implementation Roadmap](#15-implementation-roadmap)

---

## 1. Executive Summary

### 1.1 Project Overview
**Do IT** is a cutting-edge personalized fitness and diet application designed to empower users to achieve their health and fitness aspirations through highly customized workout routines and nutritional meal plans. The application intelligently generates plans based on detailed user profiles, physical attributes, and explicitly stated fitness goals. The system incorporates a flexible subscription model with various tiers, including a premium VIP option that integrates dedicated personal coaching services.

### 1.2 Key Business Objectives
- Deliver AI-powered personalized fitness and nutrition guidance
- Enable real-time communication between users and coaches through integrated chat
- Provide high-quality instructional video content via Mux streaming platform
- Support scalable user growth with serverless architecture
- Implement comprehensive subscription monetization (Basic: 300 QAR/month, VIP: 550 QAR/month)
- Ensure cross-platform compatibility with React Native mobile and Next.js admin dashboard

### 1.3 Architecture Highlights
- **Hybrid Cloud Strategy**: Firebase (GCP) for core backend services, AWS S3 for file storage, Mux for video delivery
- **Cross-Platform Development**: React Native for mobile, Next.js for web administration
- **Real-time Capabilities**: Firebase real-time database for live updates and messaging
- **Serverless Architecture**: Firebase Cloud Functions for business logic and AI integration

---

## 2. System Overview

### 2.1 System Purpose
The Do IT application ecosystem provides a complete digital fitness platform that connects users with personalized workout plans, nutritional guidance, and professional coaching through mobile and web interfaces.

### 2.2 Key Features

#### 2.2.1 User Registration & Profile Management

##### Welcome Screen & Language Selection
- **Seamless Onboarding**: Intuitive welcome flow guiding new users through essential setup steps
- **Multi-language Support**: Arabic and English language selection for global accessibility
- **User-friendly Interface**: Clear instructions and intuitive navigation throughout the registration process

##### Comprehensive User Data Collection
The application systematically collects essential personal information to facilitate highly personalized fitness and diet plans:

**Personal Information**:
- Full name and contact details
- Gender selection (male/female/other)
- Age (for accurate metabolic calculations)
- Current weight and target weight goals
- Height (for BMI and body composition calculations)
- Activity level assessment (sedentary, lightly active, moderately active, very active)
- Medical conditions or physical limitations
- Dietary restrictions and food preferences

**Technical Implementation**:
- **Authentication**: Firebase Authentication manages all user account credentials securely
- **Data Storage**: User profile data stored in dedicated 'users' collection within Firestore
- **Security**: Firestore Security Rules ensure users can only access their own personal data
- **Data Validation**: Client-side and server-side validation for data integrity

##### User Goal Selection & Customization
- **Primary Goals**: Weight loss, muscle gain, general fitness, endurance improvement
- **Secondary Goals**: Strength building, flexibility enhancement, cardiovascular health
- **Timeline Setting**: Short-term milestones and long-term goal establishment
- **Progress Milestones**: Automated milestone tracking with celebration features

##### Profile Media Management (Optional)
- **Profile Photos**: Optional profile picture upload for personalized experience
- **Progress Documentation**: Progress photos and videos for visual journey tracking
- **Technical Storage**: AWS S3 bucket integration for secure and scalable media storage
- **Firestore Integration**: Media file paths referenced in user's Firestore document
- **Privacy Controls**: User-controlled visibility settings for shared content and progress media

#### 2.2.2 Core Application Features

##### Main Dashboard
- **Centralized Hub**: Personalized main dashboard serving as the central hub for user's fitness journey
- **Quick Access**: Easy navigation to workout plans, diet plans, progress tracking, and coaching
- **Progress Overview**: Visual representation of current progress, upcoming workouts, and achievements
- **Personalized Content**: Dynamically updated content based on user's goals and subscription tier

##### AI-Powered Program Generation
The cornerstone of the "Do IT" application is its intelligent program generation engine:

**Training Plan Generation**:
- **Personalized Algorithms**: Proprietary algorithms that analyze user profile data
- **Goal-Specific Plans**: Customized workout routines based on fitness goals and experience level
- **Progressive Overload**: Automatically adjusted difficulty and intensity over time
- **Equipment Consideration**: Plans adapted to available equipment and workout environment

**Diet and Nutrition Plan Generation**:
- **Personalized Meal Plans**: Custom meal plans based on dietary preferences and restrictions
- **Calorie Calculation**: Accurate daily calorie requirements based on user metrics and goals
- **BMI Calculation**: Real-time BMI tracking and health status monitoring
- **Hydration Goals**: Personalized daily water intake recommendations

**Technical Implementation**:
- **Firebase Cloud Functions**: Generation triggered when user completes profile or requests new plan
- **Data Processing**: Functions read user profile from Firestore and apply generation algorithms
- **Plan Storage**: Generated plans written to dedicated 'plans' collection in Firestore
- **Real-time Updates**: Plans automatically updated based on progress and goal changes

##### Training and Workout Module
Comprehensive exercise guidance with detailed instructional content:

**Personalized Workout Plans**:
- **Custom Routines**: Tailored exercise sequences based on user goals and fitness level
- **Exercise Variety**: Diverse workout types including strength, cardio, flexibility, and functional training
- **Progression Tracking**: Automatic progression adjustments based on performance and feedback

**Exercise Details & Instruction**:
- **Comprehensive Information**: Number of sets, repetitions, rest periods, and intensity guidelines
- **Visual Guidance**: High-quality instructional photos for proper form demonstration
- **Video Instruction**: Professional instructional videos for each exercise
- **Technical Implementation**: Mux API integration for optimized video streaming with adaptive bitrate
- **Mobile Optimization**: Embedded Mux player ensuring smooth playback across all devices
- **Content Storage**: Video URLs and exercise data stored in 'exercises' collection in Firestore

##### Real-time Coach Communication (VIP Feature)
- **Integrated Chat System**: Real-time messaging between coaches and VIP users
- **Technical Implementation**: Firestore-powered chat with real-time synchronization
- **Message Types**: Text, images, progress updates, and workout feedback
- **Coach Assignment**: Automatic coach assignment for VIP subscribers
- **Communication History**: Complete message history and progress tracking

##### Subscription Management
- **Flexible Monetization**: Clear value-driven subscription model with Basic and VIP tiers
- **Payment Integration**: Third-party payment gateway integration (Stripe, RevenueCat)
- **Subscription Tracking**: User subscription status stored in Firestore user documents
- **Webhook Integration**: Firebase Cloud Functions as webhook listeners for payment updates
- **Automatic Renewals**: Seamless subscription renewal and upgrade/downgrade options

#### 2.2.3 Administrative Tools

##### Next.js-Based Admin Dashboard
A secure, web-based administrative dashboard built using Next.js for comprehensive application management:

**User Management**:
- **Profile Monitoring**: Comprehensive view of user profiles and progress tracking
- **Subscription Management**: Monitor and manage user subscription statuses and billing
- **Coach Assignment**: Assign and manage coach-user relationships for VIP subscribers
- **User Analytics**: Individual user engagement metrics and progress reports
- **Account Administration**: User account activation, deactivation, and support management

**Content Management**:
- **Exercise Library**: Upload and manage comprehensive exercise content database
- **Video Integration**: Seamless Mux video upload and management for exercise instructions
- **Media Management**: Organize and manage instructional photos and supplementary content
- **Plan Templates**: Create and manage workout and diet plan templates
- **Content Versioning**: Track and manage content updates and revisions

**Communication Center**:
- **Direct Messaging**: Send targeted messages to individual users or coaches
- **Broadcast System**: Push notification broadcasting to user segments (all users, Basic subscribers, VIP subscribers)
- **Announcement Management**: Create and manage platform-wide announcements
- **Support Integration**: Integrated customer support and ticket management system
- **Communication Analytics**: Track message delivery rates and user engagement

**Analytics Dashboard**:
- **User Growth Metrics**: Track new registrations, active users, and retention rates
- **Engagement Analytics**: Monitor app usage patterns, feature adoption, and user behavior
- **Revenue Tracking**: Subscription revenue, conversion rates, and financial performance metrics
- **Performance Monitoring**: System performance, API response times, and error tracking
- **Custom Reports**: Generate detailed reports for business intelligence and decision making

**Technical Implementation**:
- **Framework**: Next.js 13+ with App Router for optimal performance
- **Authentication**: Secure admin authentication with role-based access control
- **Data Visualization**: Recharts/Chart.js integration for comprehensive analytics visualization
- **Real-time Updates**: Live dashboard updates using Firestore real-time listeners
- **Security**: HTTPS communication and Firestore Security Rules for admin-only access

### 2.3 Target Users
- **End Users**: Fitness enthusiasts seeking personalized guidance
- **Coaches**: Fitness professionals providing services through the platform
- **Administrators**: Platform managers overseeing operations and content

### 2.4 Subscription Model & Monetization

#### 2.4.1 Basic Package (300 QAR/month)
- Personalized workout plans
- Customized diet plans
- BMI and calorie calculations
- Exercise instructional videos
- Progress tracking capabilities
- Basic app features and functionality

#### 2.4.2 VIP Package (550 QAR/month)
- All Basic Package features
- **Personal Coaching**: Dedicated coach assignment
- **Real-time Chat**: Direct communication with personal coach
- **Priority Support**: Enhanced customer service
- **Advanced Analytics**: Detailed progress reports and insights
- **Exclusive Content**: Premium workout routines and meal plans

---

## 3. Architecture Principles

### 3.1 Design Principles
- **Scalability**: Design for horizontal scaling to support growing user base
- **Modularity**: Loosely coupled components for independent development and deployment
- **Security**: End-to-end security with proper authentication and data protection
- **Performance**: Optimized for fast loading and responsive user experience
- **Maintainability**: Clean code architecture with comprehensive documentation

### 3.2 Technology Decisions
- **Firebase**: Chosen for rapid development, real-time capabilities, and managed infrastructure
- **React Native**: Single codebase for iOS and Android to reduce development overhead
- **Next.js**: Server-side rendering for optimal web performance
- **Mux**: Specialized video platform for high-quality streaming
- **AWS S3**: Industry-standard object storage for cost-effective file management

---

## 4. Technology Stack

### 4.1 Backend Services

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Authentication** | Firebase Auth | User registration, login, session management |
| **Database** | Firestore | NoSQL database for application data |
| **Business Logic** | Firebase Cloud Functions | Serverless functions for core operations |
| **File Storage** | AWS S3 | Static asset storage (images, documents) |
| **Video Platform** | Mux API | Video encoding, streaming, and delivery |
| **AI Services** | Google AI Platform | Machine learning and AI capabilities |

### 4.2 Client Applications

| Platform | Technology | Framework Version |
|----------|------------|-------------------|
| **Mobile App** | React Native | Latest Stable |
| **Web Admin** | Next.js | Latest Stable |
| **State Management** | Redux Toolkit | Latest Stable |
| **UI Components** | React Native Elements / Material-UI | Latest Stable |

### 4.3 Development Tools

| Category | Tool | Purpose |
|----------|------|---------|
| **Version Control** | Git | Source code management |
| **CI/CD** | GitHub Actions | Automated testing and deployment |
| **Testing** | Jest, Detox | Unit and integration testing |
| **Monitoring** | Firebase Analytics | Application performance monitoring |

---

## 5. System Context Diagram

```mermaid
graph TB
    User[End Users<br/>Mobile App] 
    Coach[Coaches<br/>Mobile App]
    Admin[Administrators<br/>Web Dashboard]
    
    DoIT[Do IT Application<br/>System]
    
    Firebase[Firebase<br/>Backend Services]
    AWS[AWS S3<br/>File Storage]
    Mux[Mux<br/>Video Platform]
    Payment[Payment<br/>Gateway]
    AI[Google AI<br/>Platform]
    
    User --> DoIT
    Coach --> DoIT
    Admin --> DoIT
    
    DoIT --> Firebase
    DoIT --> AWS
    DoIT --> Mux
    DoIT --> Payment
    DoIT --> AI
    
    style DoIT fill:#e1f5fe
    style Firebase fill:#fff3e0
    style AWS fill:#f3e5f5
    style Mux fill:#e8f5e8
```

### 5.1 External Systems
- **Payment Gateway**: [Specify payment provider - Stripe/PayPal/etc.]
- **Push Notification Service**: Firebase Cloud Messaging
- **Email Service**: [Specify email provider - SendGrid/AWS SES/etc.]
- **Analytics Platform**: Firebase Analytics, Google Analytics

---

## 6. Container Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        Mobile[React Native<br/>Mobile App<br/>iOS & Android]
        Web[Next.js<br/>Admin Dashboard<br/>Web Browser]
    end
    
    subgraph "Firebase Backend"
        Auth[Firebase<br/>Authentication]
        Firestore[Firestore<br/>Database]
        Functions[Cloud Functions<br/>Business Logic]
        Storage[Firebase<br/>Storage]
    end
    
    subgraph "External Services"
        S3[AWS S3<br/>File Storage]
        MuxAPI[Mux API<br/>Video Platform]
        AI[Google AI<br/>Platform]
    end
    
    Mobile --> Auth
    Mobile --> Firestore
    Mobile --> Functions
    Web --> Auth
    Web --> Firestore
    Web --> Functions
    
    Functions --> S3
    Functions --> MuxAPI
    Functions --> AI
    Functions --> Firestore
    
    style Mobile fill:#e3f2fd
    style Web fill:#e8f5e8
    style Auth fill:#fff3e0
    style Firestore fill:#fff3e0
    style Functions fill:#fff3e0
```

### 6.1 Container Responsibilities

#### 6.1.1 React Native Mobile App
- **Purpose**: Primary user interface for end-users and coaches
- **Responsibilities**:
  - User authentication and profile management
  - Workout and diet plan display
  - Video content playback
  - Progress tracking and data input
  - Real-time messaging with coaches
  - Push notification handling

#### 6.1.2 Next.js Admin Dashboard
- **Purpose**: Web-based administrative interface
- **Responsibilities**:
  - User management and analytics
  - Content management (videos, plans, articles)
  - Coach management and assignment
  - Payment and subscription management
  - System monitoring and reporting

#### 6.1.3 Firebase Backend
- **Purpose**: Core backend services and data management
- **Responsibilities**:
  - User authentication and authorization
  - Data persistence and real-time synchronization
  - Business logic execution
  - API endpoint management
  - Integration with external services

---

## 7. Component Architecture

### 7.1 React Native Mobile App Components

```mermaid
graph TB
    subgraph "React Native App"
        Auth[Authentication<br/>Components]
        Profile[User Profile<br/>Management]
        Workout[Workout Plan<br/>Display]
        Video[Video Player<br/>Component]
        Progress[Progress Tracking<br/>Components]
        Chat[Messaging<br/>Components]
        Navigation[Navigation<br/>System]
    end
    
    subgraph "Shared Services"
        API[API Service<br/>Layer]
        State[State Management<br/>Redux Store]
        Storage[Local Storage<br/>AsyncStorage]
    end
    
    Auth --> API
    Profile --> API
    Workout --> API
    Video --> API
    Progress --> API
    Chat --> API
    
    API --> State
    State --> Storage
```

#### 7.1.1 Key Components

| Component | Description | Key Features |
|-----------|-------------|--------------|
| **Authentication** | Login/signup flows | Social login, biometric auth, password reset |
| **User Profile** | Profile management | Personal info, preferences, goals |
| **Workout Plans** | Exercise routines | Plan display, progress tracking, timer |
| **Video Player** | Exercise videos | Streaming, offline download, playback controls |
| **Progress Tracking** | User metrics | Weight, measurements, photo progress |
| **Messaging** | Coach communication | Real-time chat, file sharing, notifications |

### 7.2 Next.js Admin Dashboard Components

```mermaid
graph TB
    subgraph "Next.js Admin Dashboard"
        Dashboard[Admin<br/>Dashboard]
        UserMgmt[User<br/>Management]
        ContentMgmt[Content<br/>Management]
        CoachMgmt[Coach<br/>Management]
        Analytics[Analytics<br/>& Reports]
        Settings[System<br/>Settings]
    end
    
    subgraph "Shared Components"
        Layout[Layout<br/>Components]
        Forms[Form<br/>Components]
        Tables[Data Table<br/>Components]
        Charts[Chart<br/>Components]
    end
    
    Dashboard --> Layout
    UserMgmt --> Forms
    UserMgmt --> Tables
    ContentMgmt --> Forms
    CoachMgmt --> Tables
    Analytics --> Charts
```

### 7.3 Firebase Cloud Functions Components

```mermaid
graph TB
    subgraph "Cloud Functions"
        UserFunc[User Management<br/>Functions]
        PlanFunc[Plan Generation<br/>Functions]
        PaymentFunc[Payment<br/>Processing]
        NotifFunc[Notification<br/>Functions]
        AIFunc[AI Integration<br/>Functions]
        DataFunc[Data Processing<br/>Functions]
    end
    
    subgraph "External Integrations"
        MuxInt[Mux API<br/>Integration]
        S3Int[AWS S3<br/>Integration]
        AIInt[Google AI<br/>Integration]
        PayInt[Payment Gateway<br/>Integration]
    end
    
    PlanFunc --> AIInt
    PaymentFunc --> PayInt
    DataFunc --> S3Int
    UserFunc --> MuxInt
```

---

## 8. Data Architecture

### 8.1 Firestore Database Structure

```json
{
  "users": {
    "userId": {
      "profile": {
        "email": "string",
        "displayName": "string",
        "photoURL": "string",
        "dateOfBirth": "timestamp",
        "gender": "string",
        "height": "number",
        "weight": "number",
        "age": "number",
        "fitnessLevel": "string",
        "goals": ["string"],
        "language": "string",
        "preferences": {
          "workoutDuration": "number",
          "workoutFrequency": "number",
          "dietaryRestrictions": ["string"]
        },
        "createdAt": "timestamp",
        "lastLogin": "timestamp"
      },
      "subscription": {
        "plan": "string", // "basic", "vip", "none"
        "status": "string", // "active", "expired", "cancelled"
        "startDate": "timestamp",
        "endDate": "timestamp",
        "paymentMethod": "string",
        "amount": "number", // 300 for basic, 550 for vip
        "currency": "QAR",
        "autoRenew": "boolean"
      },
      "progress": {
        "currentWeight": "number",
        "bmi": "number",
        "dailyCalories": "number",
        "hydrationGoal": "number",
        "measurements": {
          "chest": "number",
          "waist": "number",
          "hips": "number"
        },
        "photos": ["string"], // AWS S3 URLs
        "progressVideos": ["string"], // AWS S3 URLs
        "lastUpdated": "timestamp"
      },
      "assignedCoach": "string", // coachId for VIP users
      "role": "string" // "user", "coach", "admin"
    }
  },
  "exercises": {
    "exerciseId": {
      "name": "string",
      "description": "string",
      "targetMuscles": ["string"],
      "difficulty": "string",
      "equipment": ["string"],
      "instructions": "string",
      "photoURL": "string", // AWS S3 URL for instructional photo
      "videoURL": "string", // Mux video URL
      "muxAssetId": "string", // Mux asset identifier
      "muxPlaybackId": "string", // Mux playback identifier
      "category": "string", // "strength", "cardio", "flexibility", etc.
      "createdBy": "string",
      "createdAt": "timestamp"
    }
  },
  "workoutPlans": {
    "planId": {
      "title": "string",
      "description": "string",
      "difficulty": "string",
      "duration": "number", // total workout duration in minutes
      "targetMuscles": ["string"],
      "userId": "string", // assigned user
      "exercises": [
        {
          "exerciseId": "string", // reference to exercises collection
          "sets": "number",
          "reps": "number",
          "duration": "number", // exercise duration in seconds
          "restTime": "number", // rest time between sets in seconds
          "weight": "number", // recommended weight
          "order": "number" // exercise order in workout
        }
      ],
      "createdBy": "string", // coach or system generated
      "createdAt": "timestamp",
      "isActive": "boolean"
    }
  },
  "dietPlans": {
    "planId": {
      "title": "string",
      "description": "string",
      "totalCalories": "number",
      "macros": {
        "protein": "number",
        "carbs": "number",
        "fat": "number"
      },
      "meals": [
        {
          "mealType": "string",
          "foods": [
            {
              "name": "string",
              "quantity": "number",
              "unit": "string",
              "calories": "number"
            }
          ]
        }
      ],
      "createdBy": "string",
      "createdAt": "timestamp"
    }
  },
  "coaches": {
    "coachId": {
      "profile": {
        "name": "string",
        "email": "string",
        "photoURL": "string",
        "specializations": ["string"],
        "experience": "number",
        "rating": "number",
        "bio": "string"
      },
      "availability": {
        "schedule": "object",
        "timezone": "string"
      },
      "clients": ["userId"]
    }
  },
  "messages": {
    "conversationId": {
      "participants": ["userId", "coachId"],
      "messages": [
        {
          "senderId": "string",
          "content": "string",
          "timestamp": "timestamp",
          "type": "string",
          "attachments": ["string"]
        }
      ],
      "lastMessage": "timestamp"
    }
  }
}
```

### 8.2 Data Relationships

```mermaid
erDiagram
    USERS ||--o{ WORKOUT_PLANS : assigned
    USERS ||--o{ DIET_PLANS : assigned
    USERS ||--o{ PROGRESS : tracks
    USERS ||--o{ MESSAGES : sends
    COACHES ||--o{ USERS : coaches
    COACHES ||--o{ MESSAGES : sends
    COACHES ||--o{ WORKOUT_PLANS : creates
    COACHES ||--o{ DIET_PLANS : creates
    WORKOUT_PLANS ||--o{ EXERCISES : contains
    EXERCISES ||--o{ VIDEOS : references
```

### 8.3 Data Access Patterns

| Operation | Collection | Index Requirements | Security Rules |
|-----------|------------|-------------------|----------------|
| **User Profile** | users | userId | User can read/write own profile |
| **Workout Plans** | workoutPlans | userId, difficulty, targetMuscles | User can read assigned plans |
| **Progress Tracking** | users/progress | userId, timestamp | User can read/write own progress |
| **Coach Messages** | messages | conversationId, timestamp | Participants can read/write |
| **Admin Analytics** | All collections | Various composite indexes | Admin role required |

---

## 9. API Specifications

### 9.1 Firebase Cloud Functions API

#### 9.1.1 User Management API

```javascript
// Generate Personalized Workout Plan
exports.generateWorkoutPlan = functions.https.onCall(async (data, context) => {
  // Input validation
  const { userId, preferences, fitnessLevel, goals } = data;
  
  // Authentication check
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    // [Implement AI-based plan generation logic here]
    const workoutPlan = await generatePlanWithAI(preferences, fitnessLevel, goals);
    
    // Save to Firestore
    await admin.firestore().collection('workoutPlans').add({
      ...workoutPlan,
      userId: userId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    return { success: true, planId: workoutPlan.id };
  } catch (error) {
    throw new functions.https.HttpsError('internal', 'Failed to generate workout plan');
  }
});
```

#### 9.1.2 Progress Tracking API

```javascript
// Calculate BMI and Calorie Requirements
exports.calculateMetrics = functions.https.onCall(async (data, context) => {
  const { height, weight, age, gender, activityLevel } = data;
  
  // Authentication check
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  // BMI Calculation
  const bmi = weight / Math.pow(height / 100, 2);
  
  // Calorie Calculation (Harris-Benedict Equation)
  let bmr;
  if (gender === 'male') {
    bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
  } else {
    bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
  }
  
  const activityMultipliers = {
    sedentary: 1.2,
    light: 1.375,
    moderate: 1.55,
    active: 1.725,
    veryActive: 1.9
  };
  
  const dailyCalories = bmr * activityMultipliers[activityLevel];
  
  // Update user's progress in Firestore
  await admin.firestore().collection('users').doc(context.auth.uid).update({
    'progress.bmi': Math.round(bmi * 10) / 10,
    'progress.dailyCalories': Math.round(dailyCalories),
    'progress.currentWeight': weight,
    'progress.lastUpdated': admin.firestore.FieldValue.serverTimestamp()
  });
  
  return {
    bmi: Math.round(bmi * 10) / 10,
    dailyCalories: Math.round(dailyCalories),
    bmr: Math.round(bmr)
  };
});
```

#### 9.1.3 Subscription Management API

```javascript
// Process Subscription Payment
exports.processSubscription = functions.https.onCall(async (data, context) => {
  const { planType, paymentToken } = data; // planType: "basic" or "vip"
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    const planPricing = {
      basic: { amount: 300, currency: 'QAR' },
      vip: { amount: 550, currency: 'QAR' }
    };
    
    const plan = planPricing[planType];
    if (!plan) {
      throw new functions.https.HttpsError('invalid-argument', 'Invalid plan type');
    }
    
    // [Integrate with payment gateway - Stripe/RevenueCat]
    const paymentResult = await processPayment(paymentToken, plan.amount, plan.currency);
    
    if (paymentResult.success) {
      // Update user subscription in Firestore
      const subscriptionData = {
        plan: planType,
        status: 'active',
        startDate: admin.firestore.FieldValue.serverTimestamp(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        amount: plan.amount,
        currency: plan.currency,
        autoRenew: true,
        paymentMethod: paymentResult.paymentMethod
      };
      
      await admin.firestore().collection('users').doc(context.auth.uid).update({
        subscription: subscriptionData
      });
      
      // Assign coach for VIP users
      if (planType === 'vip') {
        const availableCoach = await findAvailableCoach();
        if (availableCoach) {
          await admin.firestore().collection('users').doc(context.auth.uid).update({
            assignedCoach: availableCoach.id
          });
        }
      }
      
      return { success: true, subscriptionId: paymentResult.subscriptionId };
    }
    
    throw new functions.https.HttpsError('internal', 'Payment processing failed');
  } catch (error) {
    functions.logger.error('Subscription error:', error);
    throw new functions.https.HttpsError('internal', 'Subscription processing failed');
  }
});
```

#### 9.1.4 Mux Video Integration API

```javascript
// Upload Exercise Video to Mux
exports.uploadExerciseVideo = functions.https.onCall(async (data, context) => {
  const { exerciseId, videoFile } = data;
  
  // Check admin/coach permissions
  if (!context.auth || !hasRole(context.auth.uid, ['admin', 'coach'])) {
    throw new functions.https.HttpsError('permission-denied', 'Insufficient permissions');
  }
  
  try {
    // Create Mux asset
    const muxAsset = await mux.Video.Assets.create({
      input: videoFile.url,
      playback_policy: 'signed', // Secure playback
      mp4_support: 'standard'
    });
    
    // Update exercise document with Mux details
    await admin.firestore().collection('exercises').doc(exerciseId).update({
      muxAssetId: muxAsset.id,
      muxPlaybackId: muxAsset.playback_ids[0].id,
      videoURL: `https://stream.mux.com/${muxAsset.playback_ids[0].id}.m3u8`,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    return { 
      success: true, 
      assetId: muxAsset.id,
      playbackId: muxAsset.playback_ids[0].id 
    };
  } catch (error) {
    functions.logger.error('Mux upload error:', error);
    throw new functions.https.HttpsError('internal', 'Video upload failed');
  }
});
```

### 9.2 REST API Endpoints

| Endpoint | Method | Purpose | Request Body | Response |
|----------|--------|---------|--------------|----------|
| `/api/users/profile` | GET | Get user profile | - | User profile object |
| `/api/users/profile` | PUT | Update profile | Profile data | Updated profile |
| `/api/plans/workout` | POST | Generate workout plan | User preferences | Plan ID |
| `/api/plans/diet` | POST | Generate diet plan | Dietary preferences | Plan ID |
| `/api/progress` | POST | Log progress | Progress data | Success status |
| `/api/videos/upload` | POST | Upload video | Video file | Video URL |
| `/api/payments/process` | POST | Process payment | Payment details | Transaction ID |

### 9.3 Real-time APIs

#### 9.3.1 Chat System (VIP Users & Admin Communication)

```javascript
// Initialize Chat for VIP User
exports.initializeChat = functions.https.onCall(async (data, context) => {
  const { coachId } = data;
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  // Verify VIP subscription
  const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
  const userData = userDoc.data();
  
  if (!userData.subscription || userData.subscription.plan !== 'vip') {
    throw new functions.https.HttpsError('permission-denied', 'VIP subscription required');
  }
  
  const chatId = `${context.auth.uid}_${coachId}`;
  
  // Create chat document
  await admin.firestore().collection('chats').doc(chatId).set({
    participants: [context.auth.uid, coachId],
    userId: context.auth.uid,
    coachId: coachId,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    lastMessage: null,
    lastMessageTime: null,
    unreadCount: {
      [context.auth.uid]: 0,
      [coachId]: 0
    }
  });
  
  return { chatId };
});

// Send Message Function
exports.sendMessage = functions.https.onCall(async (data, context) => {
  const { chatId, message, messageType = 'text' } = data;
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    const messageData = {
      text: message,
      senderId: context.auth.uid,
      messageType: messageType, // 'text', 'image', 'video', 'file'
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      edited: false
    };
    
    // Add message to subcollection
    const messageRef = await admin.firestore()
      .collection('chats').doc(chatId)
      .collection('messages').add(messageData);
    
    // Update chat metadata
    await admin.firestore().collection('chats').doc(chatId).update({
      lastMessage: message,
      lastMessageTime: admin.firestore.FieldValue.serverTimestamp(),
      [`unreadCount.${getOtherParticipant(chatId, context.auth.uid)}`]: admin.firestore.FieldValue.increment(1)
    });
    
    return { messageId: messageRef.id };
  } catch (error) {
    functions.logger.error('Send message error:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send message');
  }
});

// Real-time Message Listener (Client-side)
const listenToMessages = (chatId, callback) => {
  return db.collection('chats').doc(chatId)
    .collection('messages')
    .orderBy('timestamp', 'asc')
    .onSnapshot((snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          callback(change.doc.data());
        }
      });
    });
};

// Mark Messages as Read
exports.markMessagesAsRead = functions.https.onCall(async (data, context) => {
  const { chatId } = data;
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const batch = admin.firestore().batch();
  
  // Get unread messages
  const unreadMessages = await admin.firestore()
    .collection('chats').doc(chatId)
    .collection('messages')
    .where('read', '==', false)
    .where('senderId', '!=', context.auth.uid)
    .get();
  
  // Mark as read
  unreadMessages.docs.forEach(doc => {
    batch.update(doc.ref, { read: true });
  });
  
  // Reset unread count
  batch.update(admin.firestore().collection('chats').doc(chatId), {
    [`unreadCount.${context.auth.uid}`]: 0
  });
  
  await batch.commit();
  return { success: true };
});
```

#### 9.3.2 Push Notifications & Broadcasting

```javascript
// Send Push Notification
exports.sendPushNotification = functions.https.onCall(async (data, context) => {
  const { userIds, title, body, data: notificationData } = data;
  
  // Check admin permissions
  if (!context.auth || !hasRole(context.auth.uid, ['admin', 'coach'])) {
    throw new functions.https.HttpsError('permission-denied', 'Insufficient permissions');
  }
  
  try {
    const tokens = [];
    
    // Get FCM tokens for target users
    for (const userId of userIds) {
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      const userData = userDoc.data();
      if (userData.fcmToken) {
        tokens.push(userData.fcmToken);
      }
    }
    
    if (tokens.length === 0) {
      return { success: false, message: 'No valid tokens found' };
    }
    
    const message = {
      notification: { title, body },
      data: notificationData || {},
      tokens: tokens
    };
    
    const response = await admin.messaging().sendMulticast(message);
    
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    functions.logger.error('Push notification error:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// Broadcast to User Segments
exports.broadcastToSegment = functions.https.onCall(async (data, context) => {
  const { segment, title, body } = data; // segment: 'all', 'basic', 'vip', 'inactive'
  
  if (!context.auth || !hasRole(context.auth.uid, ['admin'])) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }
  
  let query = admin.firestore().collection('users');
  
  // Apply segment filters
  switch (segment) {
    case 'basic':
      query = query.where('subscription.plan', '==', 'basic');
      break;
    case 'vip':
      query = query.where('subscription.plan', '==', 'vip');
      break;
    case 'inactive':
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      query = query.where('lastLogin', '<', thirtyDaysAgo);
      break;
  }
  
  const users = await query.get();
  const tokens = users.docs
    .map(doc => doc.data().fcmToken)
    .filter(token => token);
  
  if (tokens.length === 0) {
    return { success: false, message: 'No users found in segment' };
  }
  
  const message = {
    notification: { title, body },
    tokens: tokens
  };
  
  const response = await admin.messaging().sendMulticast(message);
  
  return {
    success: true,
    targetUsers: users.size,
    successCount: response.successCount,
    failureCount: response.failureCount
  };
});
```

---

## 10. Security Architecture

### 10.1 Authentication & Authorization

```mermaid
graph TB
    User[User] --> Auth[Firebase Auth]
    Auth --> Token[JWT Token]
    Token --> Rules[Security Rules]
    Rules --> Data[Firestore Data]
    
    subgraph "Security Layers"
        Auth
        Rules
        Functions[Cloud Functions<br/>Authorization]
        Encryption[Data Encryption]
    end
    
    Functions --> Data
    Encryption --> Data
```

### 10.2 Security Rules

#### 10.2.1 Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    function isAdminOrCoach() {
      return hasRole('admin') || hasRole('coach');
    }
    
    function hasVIPSubscription() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.subscription.plan == 'vip';
    }
    
    // Users collection - strict user data protection
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdminOrCoach();
      allow write: if isOwner(userId) && 
        // Prevent users from modifying sensitive fields
        !('role' in request.resource.data.diff(resource.data).affectedKeys()) &&
        !('subscription.status' in request.resource.data.diff(resource.data).affectedKeys());
      
      // Admin-only operations
      allow update: if isAdminOrCoach() && 
        // Admins can update role and subscription
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['role', 'subscription', 'assignedCoach']);
    }
    
    // Exercise collection - content management
    match /exercises/{exerciseId} {
      allow read: if isAuthenticated();
      allow write: if isAdminOrCoach();
    }
    
    // Workout plans - user-specific with coach access
    match /workoutPlans/{planId} {
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || 
         isAdminOrCoach());
      allow write: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Diet plans - similar to workout plans
    match /dietPlans/{planId} {
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || 
         isAdminOrCoach());
      allow write: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Chat system - VIP users and coaches only
    match /chats/{chatId} {
      allow read, write: if isAuthenticated() && 
        (request.auth.uid in resource.data.participants || 
         hasVIPSubscription() || 
         isAdminOrCoach());
      
      // Messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated() && 
          (request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants ||
           isAdminOrCoach());
        allow create: if isAuthenticated() && 
          (request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants ||
           hasVIPSubscription() ||
           isAdminOrCoach()) &&
          request.resource.data.senderId == request.auth.uid;
        allow update: if isAuthenticated() && 
          resource.data.senderId == request.auth.uid &&
          // Only allow marking as read or editing own messages
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read', 'edited', 'text']);
      }
    }
    
    // Messages - only conversation participants
    match /messages/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    
    // Progress tracking - user-specific
    match /progress/{userId} {
      allow read, write: if isOwner(userId) || isAdminOrCoach();
    }
    
    // Notifications - user-specific
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow create: if isAdminOrCoach();
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid &&
        // Users can only mark as read
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
    }
    
    // Analytics - admin only
    match /analytics/{document=**} {
      allow read, write: if hasRole('admin');
    }
    
    // System configuration - admin only
    match /config/{document=**} {
      allow read, write: if hasRole('admin');
    }
    
    // Subscription transactions - read-only for users, write for admins
    match /transactions/{transactionId} {
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdminOrCoach());
      allow write: if hasRole('admin');
    }
  }
}
```

### 10.3 Data Protection

| Data Type | Protection Method | Implementation |
|-----------|------------------|----------------|
| **Personal Information** | Encryption at rest | Firebase default encryption |
| **Payment Data** | PCI DSS compliance | Third-party payment processor |
| **Video Content** | Access control | Signed URLs with expiration |
| **API Communications** | HTTPS/TLS | Enforced across all endpoints |
| **File Uploads** | Virus scanning | [Implement virus scanning service] |

### 10.4 Privacy Compliance

- **GDPR Compliance**: Data export, deletion, and consent management
- **CCPA Compliance**: California privacy rights implementation
- **Data Retention**: Automated data cleanup policies
- **Audit Logging**: Comprehensive access and modification logs

---

## 11. Deployment Architecture

### 11.1 Environment Strategy

```mermaid
graph TB
    subgraph "Development"
        DevMobile[React Native<br/>Development]
        DevWeb[Next.js<br/>Development]
        DevFirebase[Firebase<br/>Development Project]
    end
    
    subgraph "Staging"
        StageMobile[React Native<br/>TestFlight/Internal Testing]
        StageWeb[Next.js<br/>Staging Environment]
        StageFirebase[Firebase<br/>Staging Project]
    end
    
    subgraph "Production"
        ProdMobile[React Native<br/>App Store/Play Store]
        ProdWeb[Next.js<br/>Production]
        ProdFirebase[Firebase<br/>Production Project]
    end
    
    DevMobile --> StageMobile
    DevWeb --> StageWeb
    DevFirebase --> StageFirebase
    
    StageMobile --> ProdMobile
    StageWeb --> ProdWeb
    StageFirebase --> ProdFirebase
```

### 11.2 CI/CD Pipeline

```yaml
# GitHub Actions Workflow Example
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Tests
        run: |
          npm install
          npm run test
          npm run lint

  deploy-functions:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy Cloud Functions
        run: |
          firebase deploy --only functions --project production

  deploy-web:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy Next.js
        run: |
          npm run build
          # [Deploy to hosting platform]

  build-mobile:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build React Native
        run: |
          # [Build and deploy to app stores]
```

### 11.3 Infrastructure Requirements

| Component | Hosting | Scaling | Backup |
|-----------|---------|---------|--------|
| **Firebase Functions** | Google Cloud | Auto-scaling | Automatic |
| **Firestore** | Google Cloud | Auto-scaling | Point-in-time recovery |
| **Next.js Web App** | [Vercel/Netlify/AWS] | Auto-scaling | Git-based |
| **React Native App** | App Stores | N/A | Source control |
| **AWS S3** | Amazon Web Services | Auto-scaling | Cross-region replication |

---

## 12. Non-Functional Requirements & Performance

### 12.1 Security Requirements

#### 12.1.1 Communication Security
- **HTTPS Enforcement**: All communication between client applications and backend services must be conducted over HTTPS
- **Certificate Management**: SSL/TLS certificates with automatic renewal and strong encryption protocols
- **API Security**: Secure API endpoints with proper authentication and authorization headers
- **Data Transmission**: End-to-end encryption for sensitive data transmission

#### 12.1.2 Data Access Security
- **Firestore Security Rules**: Comprehensive security rules ensuring users can only access their own data
- **Role-Based Access Control**: Granular permissions for users, coaches, and administrators
- **Authentication Verification**: Multi-layer authentication checks for all data operations
- **Data Isolation**: Strict data segregation between user accounts and subscription tiers

#### 12.1.3 Application Security
- **Input Validation**: Client-side and server-side validation for all user inputs
- **SQL Injection Prevention**: Parameterized queries and input sanitization
- **XSS Protection**: Content Security Policy and input encoding
- **Session Management**: Secure session handling with automatic timeout and renewal

### 12.2 Scalability & Availability

#### 12.2.1 Serverless Architecture Benefits
- **Automatic Scaling**: Firebase Cloud Functions and Firestore automatically scale with user load
- **Cost Efficiency**: Pay-per-use model with no idle resource costs
- **Global Distribution**: Multi-region deployment for optimal performance worldwide
- **Load Balancing**: Automatic traffic distribution across available resources

#### 12.2.2 High Availability Infrastructure
- **Google Cloud Platform**: Leveraging Google's high-availability infrastructure for Firebase services
- **AWS Integration**: Utilizing AWS S3's 99.*********% (11 9's) durability for file storage
- **Mux Platform**: Professional video streaming platform with global CDN and 99.9% uptime SLA
- **Target Availability**: 99.9% system uptime with automatic failover and recovery

#### 12.2.3 Performance Optimization
- **Adaptive Bitrate Streaming**: Mux API provides optimized video delivery based on network conditions
- **Content Delivery Network**: Global CDN for static assets and media content
- **Database Optimization**: Firestore's automatic indexing and query optimization
- **Caching Strategy**: Multi-level caching for frequently accessed data and content

### 12.3 Performance Targets

| Metric | Target | Measurement |
|--------|--------|-------------|
| **App Launch Time** | < 3 seconds | Time to interactive |
| **API Response Time** | < 500ms | 95th percentile |
| **Video Load Time** | < 2 seconds | First frame display |
| **Database Queries** | < 100ms | Average response time |
| **File Upload** | < 30 seconds | 10MB file |
| **System Availability** | 99.9% | Monthly uptime |
| **Data Durability** | 99.*********% | AWS S3 standard |

### 12.2 Scalability Strategy

#### 12.2.1 Database Scaling
- **Firestore**: Automatic horizontal scaling
- **Indexing Strategy**: Composite indexes for complex queries
- **Data Partitioning**: User-based data distribution
- **Caching**: Redis for frequently accessed data

#### 12.2.2 Function Scaling
- **Concurrent Executions**: Up to 1000 concurrent functions
- **Memory Allocation**: Optimized per function type
- **Cold Start Optimization**: Keep functions warm for critical paths

#### 12.2.3 Content Delivery
- **CDN**: Global content distribution for static assets
- **Video Streaming**: Adaptive bitrate streaming via Mux
- **Image Optimization**: Automatic compression and format conversion

### 12.3 Monitoring & Alerting

```javascript
// Performance monitoring setup
import { getPerformance } from 'firebase/performance';

const perf = getPerformance();

// Custom metrics
const customTrace = perf.trace('workout_plan_generation');
customTrace.start();
// [Execute workout plan generation]
customTrace.stop();

// Error tracking
import * as Sentry from '@sentry/react-native';

Sentry.captureException(error);
```

---

## 13. Admin Dashboard Specifications

### 13.1 Next.js Admin Dashboard Architecture

#### 13.1.1 Dashboard Overview
The admin dashboard provides comprehensive management capabilities for the "Do IT" application, built with Next.js for optimal performance and SEO.

```typescript
// Dashboard Layout Structure
interface DashboardLayout {
  sidebar: {
    userManagement: string;
    contentManagement: string;
    communication: string;
    analytics: string;
    settings: string;
  };
  header: {
    notifications: string;
    userProfile: string;
    logout: string;
  };
  mainContent: ReactNode;
}
```

#### 13.1.2 User Management Module

```typescript
// User Management Interface
interface UserManagementProps {
  users: User[];
  filters: {
    subscriptionType: 'all' | 'basic' | 'vip' | 'none';
    status: 'active' | 'inactive' | 'all';
    dateRange: DateRange;
  };
  actions: {
    viewProfile: (userId: string) => void;
    updateSubscription: (userId: string, plan: SubscriptionPlan) => void;
    assignCoach: (userId: string, coachId: string) => void;
    sendMessage: (userId: string, message: string) => void;
  };
}

// User Profile View Component
const UserProfileView: React.FC<{ userId: string }> = ({ userId }) => {
  const { user, progress, workoutPlans, dietPlans } = useUserData(userId);
  
  return (
    <div className="user-profile-container">
      <UserBasicInfo user={user} />
      <ProgressCharts progress={progress} />
      <PlanHistory workoutPlans={workoutPlans} dietPlans={dietPlans} />
      <SubscriptionDetails subscription={user.subscription} />
      <CoachAssignment userId={userId} currentCoach={user.assignedCoach} />
    </div>
  );
};
```

#### 13.1.3 Content Management System

```typescript
// Exercise Content Management
interface ExerciseManagementProps {
  exercises: Exercise[];
  categories: ExerciseCategory[];
  actions: {
    createExercise: (exercise: CreateExerciseRequest) => void;
    updateExercise: (id: string, updates: Partial<Exercise>) => void;
    uploadVideo: (exerciseId: string, videoFile: File) => void;
    uploadPhoto: (exerciseId: string, photoFile: File) => void;
    deleteExercise: (id: string) => void;
  };
}

// Mux Video Upload Component
const VideoUploadComponent: React.FC<{ exerciseId: string }> = ({ exerciseId }) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [muxAssetId, setMuxAssetId] = useState<string | null>(null);
  
  const handleVideoUpload = async (file: File) => {
    try {
      // Upload to temporary storage first
      const tempUrl = await uploadToS3(file);
      
      // Create Mux asset
      const result = await uploadExerciseVideo({
        exerciseId,
        videoFile: { url: tempUrl }
      });
      
      setMuxAssetId(result.assetId);
      // Update exercise with Mux details
    } catch (error) {
      console.error('Video upload failed:', error);
    }
  };
  
  return (
    <div className="video-upload-container">
      <FileUploader
        accept="video/*"
        onUpload={handleVideoUpload}
        progress={uploadProgress}
      />
      {muxAssetId && (
        <MuxVideoPreview assetId={muxAssetId} />
      )}
    </div>
  );
};
```

#### 13.1.4 Communication Center

```typescript
// Communication Dashboard
interface CommunicationCenterProps {
  activeChats: Chat[];
  notifications: NotificationTemplate[];
  broadcastHistory: BroadcastMessage[];
  actions: {
    sendDirectMessage: (userId: string, message: string) => void;
    broadcastToSegment: (segment: UserSegment, message: BroadcastMessage) => void;
    createNotificationTemplate: (template: NotificationTemplate) => void;
    scheduleNotification: (notification: ScheduledNotification) => void;
  };
}

// Broadcast Message Component
const BroadcastCenter: React.FC = () => {
  const [selectedSegment, setSelectedSegment] = useState<UserSegment>('all');
  const [message, setMessage] = useState('');
  const [scheduledTime, setScheduledTime] = useState<Date | null>(null);
  
  const handleBroadcast = async () => {
    try {
      const result = await broadcastToSegment({
        segment: selectedSegment,
        title: message.title,
        body: message.body
      });
      
      toast.success(`Message sent to ${result.successCount} users`);
    } catch (error) {
      toast.error('Broadcast failed');
    }
  };
  
  return (
    <div className="broadcast-center">
      <SegmentSelector
        value={selectedSegment}
        onChange={setSelectedSegment}
        options={['all', 'basic', 'vip', 'inactive']}
      />
      <MessageComposer
        value={message}
        onChange={setMessage}
        supportedTypes={['text', 'rich', 'push']}
      />
      <ScheduleSelector
        value={scheduledTime}
        onChange={setScheduledTime}
      />
      <Button onClick={handleBroadcast}>
        Send Broadcast
      </Button>
    </div>
  );
};
```

#### 13.1.5 Analytics Dashboard

```typescript
// Analytics Interface
interface AnalyticsDashboardProps {
  metrics: {
    userGrowth: UserGrowthMetrics;
    engagement: EngagementMetrics;
    revenue: RevenueMetrics;
    retention: RetentionMetrics;
  };
  timeRange: TimeRange;
  filters: AnalyticsFilters;
}

// Revenue Analytics Component
const RevenueAnalytics: React.FC = () => {
  const { data: revenueData } = useAnalytics('revenue', {
    timeRange: '30d',
    breakdown: 'subscription_type'
  });
  
  return (
    <div className="revenue-analytics">
      <MetricCard
        title="Monthly Recurring Revenue"
        value={revenueData.mrr}
        change={revenueData.mrrChange}
        format="currency"
      />
      <ChartContainer>
        <LineChart
          data={revenueData.timeline}
          xAxis="date"
          yAxis="revenue"
          breakdown="subscription_type"
        />
      </ChartContainer>
      <SubscriptionBreakdown
        basic={revenueData.basicSubscriptions}
        vip={revenueData.vipSubscriptions}
      />
    </div>
  );
};
```

### 13.2 Development Guidelines

#### 13.2.1 Technology Stack Requirements

##### React Native Mobile Application
- **Framework**: React Native (Latest Stable Version)
- **State Management**: Redux Toolkit for global application state
- **Navigation**: React Navigation v6 for seamless screen transitions
- **UI Components**: React Native Elements or NativeBase for consistent design
- **Video Playback**: Mux React Native SDK for optimized video streaming
- **Authentication**: Firebase Authentication SDK integration
- **Database**: Firestore SDK for real-time data synchronization
- **File Storage**: AWS S3 SDK for media upload and management
- **Testing**: Jest + React Native Testing Library for comprehensive testing
- **Performance**: React Native Performance monitoring integration

##### Next.js Admin Dashboard
- **Framework**: Next.js 13+ with App Router for optimal performance and SEO
- **Styling**: Tailwind CSS for utility-first responsive design
- **State Management**: Zustand for client-side state + SWR for server state
- **Authentication**: Firebase Admin SDK with secure session management
- **Database**: Firestore Admin SDK for administrative operations
- **Charts & Analytics**: Recharts or Chart.js for comprehensive data visualization
- **Video Management**: Mux API integration for video upload and management
- **Testing**: Jest + Testing Library for component and integration testing
- **Deployment**: Vercel or Netlify for optimized hosting

##### Firebase Backend Services
- **Authentication**: Firebase Authentication for secure user management
- **Database**: Firestore for NoSQL document-based data storage
- **Cloud Functions**: Firebase Cloud Functions (TypeScript) for serverless business logic
- **Security**: Comprehensive Firestore Security Rules for data protection
- **Monitoring**: Firebase Performance Monitoring and Analytics
- **Deployment**: Firebase CLI with automated CI/CD integration

##### External Service Integrations
- **Video Platform**: Mux API for professional video encoding and streaming
- **File Storage**: AWS S3 for scalable media storage with CDN integration
- **Payment Processing**: Third-party payment gateway (Stripe/RevenueCat)
- **Push Notifications**: Firebase Cloud Messaging for user engagement
- **AI Services**: Google AI Platform for intelligent plan generation

#### 13.2.2 Code Standards & Best Practices

##### Security Implementation
- **HTTPS Enforcement**: All client-server communication over HTTPS
- **Data Validation**: Client-side and server-side input validation
- **Authentication Checks**: Multi-layer authentication verification
- **Access Control**: Role-based permissions (user, coach, admin)
- **Data Encryption**: End-to-end encryption for sensitive data transmission

#### 13.2.2 React Native Standards
```javascript
// Component structure example
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';

const WorkoutPlanScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(false);
  const user = useSelector(state => state.user);
  const dispatch = useDispatch();

  useEffect(() => {
    // [Component initialization logic]
  }, []);

  return (
    <View style={styles.container}>
      {/* [Component JSX] */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
});

export default WorkoutPlanScreen;
```

#### 13.2.3 Firebase Functions Standards
```javascript
// Function structure example
const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.functionName = functions.https.onCall(async (data, context) => {
  // Input validation
  if (!data.requiredField) {
    throw new functions.https.HttpsError('invalid-argument', 'Missing required field');
  }

  // Authentication check
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    // [Function logic here]
    return { success: true, data: result };
  } catch (error) {
    functions.logger.error('Function error:', error);
    throw new functions.https.HttpsError('internal', 'Internal server error');
  }
});
```

### 13.2 Testing Strategy

| Test Type | Framework | Coverage Target | Automation |
|-----------|-----------|-----------------|------------|
| **Unit Tests** | Jest | 80% | CI/CD Pipeline |
| **Integration Tests** | Jest + Firebase Emulator | 70% | CI/CD Pipeline |
| **E2E Tests** | Detox (Mobile), Cypress (Web) | Critical paths | Nightly builds |
| **Performance Tests** | Lighthouse, Firebase Performance | Key metrics | Weekly |

### 13.3 Documentation Standards

- **Code Comments**: JSDoc format for all public functions
- **API Documentation**: OpenAPI/Swagger specifications
- **Component Documentation**: Storybook for UI components
- **Architecture Updates**: Update this document for major changes

---

## 14. Risk Assessment

### 14.1 Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| **Firebase Vendor Lock-in** | High | Medium | Implement abstraction layer for core services |
| **Video Streaming Costs** | Medium | High | Implement usage monitoring and optimization |
| **Mobile App Store Rejection** | High | Low | Follow platform guidelines, thorough testing |
| **Data Privacy Violations** | High | Low | Regular compliance audits, privacy by design |
| **Performance Degradation** | Medium | Medium | Continuous monitoring, performance budgets |

### 14.2 Business Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| **Rapid User Growth** | Medium | High | Auto-scaling infrastructure, capacity planning |
| **Competitor Features** | Medium | High | Agile development, regular feature updates |
| **Payment Processing Issues** | High | Low | Multiple payment providers, robust error handling |
| **Coach Availability** | Medium | Medium | Coach pool expansion, automated matching |

### 14.3 Security Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| **Data Breach** | High | Low | Encryption, access controls, security audits |
| **API Abuse** | Medium | Medium | Rate limiting, authentication, monitoring |
| **Unauthorized Access** | High | Low | Multi-factor authentication, session management |
| **Video Content Piracy** | Medium | Medium | DRM protection, signed URLs, watermarking |

---

## 15. Implementation Roadmap

### 15.1 Phase 1: Foundation & Core Infrastructure (Months 1-3)

#### Month 1: Project Setup & Authentication
- **Week 1-2**: 
  - Firebase project setup and configuration
  - Next.js admin dashboard scaffolding with App Router
  - React Native mobile app initialization
  - Development environment setup and CI/CD pipeline
  - AWS S3 bucket configuration for media storage
- **Week 3-4**:
  - Firebase Authentication implementation (email, social login)
  - Multi-language support setup (Arabic, English)
  - User registration flow with comprehensive data collection
  - Basic user profile management system
  - Initial Firestore security rules implementation

#### Month 2: User Management & Profile System
- **Week 5-6**:
  - Complete user profile data collection (age, weight, height, goals)
  - BMI and calorie calculation algorithms implementation
  - User goal selection and fitness preference management
  - Profile media upload functionality (AWS S3 integration)
- **Week 7-8**:
  - Admin dashboard user management module
  - User role system implementation (admin, coach, user)
  - Advanced Firestore security rules for data protection
  - User data validation, sanitization, and privacy controls

#### Month 3: Exercise Database & Content Management
- **Week 9-10**:
  - Exercise collection structure and comprehensive data models
  - Mux video integration and upload system
  - Exercise photo management with AWS S3
  - Content management system in admin dashboard
- **Week 11-12**:
  - Exercise categorization and advanced filtering
  - Video streaming optimization with Mux adaptive bitrate
  - Exercise search and recommendation engine
  - Content moderation and approval workflow

### 15.2 Phase 2: Core Application Features (Months 4-6)

#### Month 4: AI-Powered Plan Generation
- **Week 13-14**:
  - Intelligent workout plan generation algorithms
  - Personalized diet plan creation based on user profiles
  - Plan customization and modification features
  - Integration with comprehensive exercise database
- **Week 15-16**:
  - Plan assignment and scheduling system
  - Progress tracking implementation with BMI updates
  - Plan effectiveness analytics and user feedback
  - Hydration goals and calorie tracking features

#### Month 5: Subscription & Monetization
- **Week 17-18**:
  - Subscription tier implementation (Basic 300 QAR, VIP 550 QAR)
  - Payment gateway integration (Stripe/RevenueCat)
  - Subscription management in admin dashboard
  - Billing, invoice generation, and payment webhooks
- **Week 19-20**:
  - Coach assignment system for VIP users
  - Subscription analytics and revenue reporting
  - Payment failure handling and retry logic
  - Subscription upgrade/downgrade flows with prorating

#### Month 6: Real-time Communication System
- **Week 21-22**:
  - VIP chat system implementation with Firestore
  - Real-time messaging with message encryption
  - Chat history, media sharing, and file uploads
  - Coach-client communication interface
- **Week 23-24**:
  - Push notification system with FCM
  - Admin broadcast messaging to user segments
  - Notification templates and scheduling system
  - Communication analytics and monitoring dashboard

### 15.3 Phase 3: Advanced Features & Analytics (Months 7-9)

#### Month 7: Analytics & Reporting Dashboard
- **Week 25-26**:
  - User engagement analytics and behavior tracking
  - Revenue tracking and subscription metrics
  - KPI dashboard with real-time updates
  - User retention analysis and churn prediction
- **Week 27-28**:
  - Advanced admin dashboard features
  - Custom report generation and data export
  - Performance monitoring and alerting system
  - Coach performance analytics and metrics

#### Month 8: Mobile App Optimization & UX
- **Week 29-30**:
  - Mobile app performance optimization
  - Offline functionality for workout plans
  - Video caching and streaming optimization
  - Battery and data usage optimization
- **Week 31-32**:
  - Advanced UI/UX improvements and animations
  - Accessibility features implementation (WCAG 2.1)
  - Multi-language support completion
  - User onboarding flow optimization and tutorials

#### Month 9: Coach Tools & Advanced Communication
- **Week 33-34**:
  - Comprehensive coach dashboard and management tools
  - Advanced progress tracking with visual charts
  - Custom workout/diet plan creation tools for coaches
  - Coach performance analytics and client management
- **Week 35-36**:
  - Enhanced messaging features (voice messages, file sharing)
  - Coach scheduling and availability management
  - Client-coach matching algorithms
  - Advanced notification system for coaches

### 15.4 Phase 4: Security, Testing & Launch (Months 10-12)

#### Month 10: Security & Compliance
- **Week 37-38**:
  - Comprehensive security audit and vulnerability assessment
  - Firestore security rules optimization and testing
  - Data encryption and privacy compliance (GDPR)
  - Security monitoring and incident response setup
- **Week 39-40**:
  - Penetration testing and security validation
  - Data backup and disaster recovery implementation
  - Privacy policy and terms of service finalization
  - Security documentation and training

#### Month 11: Performance & Load Testing
- **Week 41-42**:
  - Load testing and performance optimization
  - Database query optimization and indexing
  - CDN setup and content delivery optimization
  - Scalability testing and auto-scaling configuration
- **Week 43-44**:
  - Mobile app performance testing and optimization
  - Video streaming performance validation
  - API rate limiting and throttling implementation
  - Error handling and comprehensive logging

#### Month 12: Beta Testing & Production Launch
- **Week 45-46**:
  - Closed beta testing with 100+ selected users
  - Bug fixes and performance improvements
  - User feedback integration and feature refinement
  - Final feature testing and validation
- **Week 47-48**:
  - Production deployment and monitoring setup
  - App store submission and approval process
  - Marketing website and comprehensive documentation
  - Post-launch support and monitoring system

### 15.5 Success Metrics & KPIs

#### User Acquisition & Engagement
- **Target**: 1,000+ registered users in first 3 months
- **Monthly Active Users**: 70% retention rate
- **Session Duration**: Average 15+ minutes per session
- **Feature Adoption**: 80% of users complete profile setup

#### Revenue & Conversion
- **Subscription Conversion**: 15% conversion rate from free to paid
- **Revenue Target**: 100,000 QAR monthly recurring revenue by month 6
- **Average Revenue Per User (ARPU)**: 400 QAR/month
- **Churn Rate**: <5% monthly churn for paid subscribers

#### Technical Performance
- **App Load Time**: <2 seconds initial load
- **Video Streaming**: <3 seconds to start playback
- **System Uptime**: 99.9% availability
- **API Response Time**: <500ms for 95% of requests

#### User Satisfaction
- **App Store Rating**: 4.5+ stars on both iOS and Android
- **Net Promoter Score (NPS)**: 50+ score
- **Support Response Time**: <2 hours for VIP users
- **Bug Reports**: <1% of active users report issues monthly

---

## Appendices

### Appendix A: Glossary
- **BMI**: Body Mass Index - a measure of body fat based on height and weight
- **BMR**: Basal Metabolic Rate - the number of calories required to keep your body functioning at rest
- **CDN**: Content Delivery Network - a geographically distributed group of servers
- **DRM**: Digital Rights Management - technologies used to control access to copyrighted material
- **JWT**: JSON Web Token - a compact, URL-safe means of representing claims

### Appendix B: External Dependencies
- [List all third-party services and their SLA requirements]
- [Document API rate limits and usage quotas]
- [Include contact information for vendor support]

### Appendix C: Compliance Requirements
- [Detail specific GDPR compliance measures]
- [Document HIPAA requirements if handling health data]
- [Include accessibility standards (WCAG 2.1)]

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Next Review**: [Date + 3 months]  
**Document Owner**: Lead Solutions Architect  
**Approved By**: [Project Stakeholders]