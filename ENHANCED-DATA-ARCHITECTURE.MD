# Enhanced Data Architecture Specification

## Table of Contents

1. [Overview](#overview)
2. [Database Design Principles](#database-design-principles)
3. [Firestore Collection Structure](#firestore-collection-structure)
4. [Data Models and Schemas](#data-models-and-schemas)
5. [Relationships and References](#relationships-and-references)
6. [Indexing Strategy](#indexing-strategy)
7. [Data Validation Rules](#data-validation-rules)
8. [Security Rules](#security-rules)
9. [Data Migration Strategy](#data-migration-strategy)
10. [Performance Optimization](#performance-optimization)

---

## 1. Overview

### 1.1 Database Architecture
The Do IT application uses **Firestore** as the primary NoSQL database, designed for real-time synchronization, offline support, and scalable performance.

### 1.2 Key Design Goals
- **Scalability**: Support millions of users and documents
- **Performance**: Sub-100ms query response times
- **Security**: Granular access control and data protection
- **Flexibility**: Schema evolution without breaking changes
- **Real-time**: Live updates for collaborative features

### 1.3 Data Flow Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        A[React Native App]
        B[Next.js Admin]
    end
    
    subgraph "Firebase Services"
        C[Firestore Database]
        D[Firebase Auth]
        E[Cloud Functions]
        F[Firebase Storage]
    end
    
    subgraph "External Services"
        G[AWS S3]
        H[Mux Video API]
        I[Payment Gateway]
    end
    
    A --> C
    A --> D
    B --> C
    B --> D
    C --> E
    E --> G
    E --> H
    E --> I
    F --> G
```

---

## 2. Database Design Principles

### 2.1 NoSQL Best Practices

#### Denormalization Strategy
- Duplicate frequently accessed data to reduce reads
- Maintain data consistency through Cloud Functions
- Balance between storage cost and query performance

#### Document Structure
- Keep documents under 1MB limit
- Use subcollections for one-to-many relationships
- Implement pagination for large datasets

#### Query Optimization
- Design collections around query patterns
- Use composite indexes for complex queries
- Implement efficient pagination with cursors

### 2.2 Data Consistency Model

#### Eventual Consistency
- Accept temporary inconsistencies for better performance
- Use transactions for critical operations
- Implement conflict resolution strategies

#### Strong Consistency
- Use transactions for financial operations
- Ensure data integrity for user profiles
- Maintain referential integrity through Cloud Functions

---

## 3. Firestore Collection Structure

### 3.1 Root Collections

```
firestore/
├── users/                          # User profiles and authentication data
├── exercises/                      # Exercise database with instructions
├── workoutPlans/                   # Generated workout plans
├── dietPlans/                      # Nutritional meal plans
├── userProgress/                   # User progress tracking
├── subscriptions/                  # Subscription and billing data
├── coaches/                        # Coach profiles and availability
├── chats/                          # Real-time messaging
├── notifications/                  # Push notification logs
├── fitnessGoals/                   # Available fitness goals
├── foodDatabase/                   # Nutritional food information
├── adminSettings/                  # Application configuration
└── analytics/                      # Usage analytics and metrics
```

### 3.2 Subcollection Structure

```
users/{userId}/
├── workoutHistory/                 # Completed workout sessions
├── measurements/                   # Body measurements over time
├── preferences/                    # User preferences and settings
├── achievements/                   # Unlocked achievements
└── deviceTokens/                   # FCM device tokens

workoutPlans/{planId}/
├── exercises/                      # Individual exercises in plan
├── progressTracking/               # Plan-specific progress
└── modifications/                  # User modifications to plan

chats/{chatId}/
├── messages/                       # Chat messages
├── participants/                   # Chat participants
└── media/                          # Shared media files
```

---

## 4. Data Models and Schemas

### 4.1 User Profile Schema

```javascript
// Collection: users/{userId}
{
  // Identity
  uid: "string",                    // Firebase Auth UID
  email: "string",                  // User email address
  emailVerified: boolean,           // Email verification status
  
  // Personal Information
  firstName: "string",              // First name (2-50 chars)
  lastName: "string",               // Last name (2-50 chars)
  displayName: "string",            // Full name for display
  gender: "male|female|other",      // Gender selection
  dateOfBirth: timestamp,           // Birth date for age calculation
  
  // Physical Metrics
  height: {
    value: number,                  // Height value
    unit: "cm|ft",                 // Height unit
    lastUpdated: timestamp          // Last measurement date
  },
  weight: {
    current: number,                // Current weight
    target: number,                 // Target weight
    unit: "kg|lbs",                // Weight unit
    history: [                      // Weight history
      {
        value: number,
        date: timestamp,
        note: "string"
      }
    ]
  },
  
  // Fitness Profile
  fitnessLevel: "beginner|intermediate|advanced",
  activityLevel: "sedentary|light|moderate|active|very_active",
  goals: [                          // Array of goal references
    {
      goalId: "string",
      priority: number,
      targetDate: timestamp,
      isActive: boolean
    }
  ],
  medicalConditions: [              // Health considerations
    {
      condition: "string",
      severity: "mild|moderate|severe",
      notes: "string"
    }
  ],
  
  // Preferences
  language: "en|ar",               // Language preference
  timezone: "string",              // User timezone
  units: {
    weight: "kg|lbs",
    height: "cm|ft",
    distance: "km|miles",
    temperature: "celsius|fahrenheit"
  },
  notifications: {
    workoutReminders: boolean,
    mealReminders: boolean,
    progressUpdates: boolean,
    coachMessages: boolean,
    marketing: boolean
  },
  
  // Subscription
  subscriptionTier: "free|basic|vip",
  subscriptionStatus: "active|inactive|cancelled|expired",
  subscriptionExpiry: timestamp,
  
  // Coach Assignment (VIP only)
  assignedCoach: {
    coachId: "string",
    assignedDate: timestamp,
    isActive: boolean
  },
  
  // System Fields
  createdAt: timestamp,
  updatedAt: timestamp,
  lastLoginAt: timestamp,
  profileComplete: boolean,
  isActive: boolean,
  
  // Privacy Settings
  privacy: {
    profileVisibility: "public|friends|private",
    shareProgress: boolean,
    allowCoachContact: boolean
  }
}
```

### 4.2 Exercise Schema

```javascript
// Collection: exercises/{exerciseId}
{
  // Basic Information
  id: "string",                     // Unique exercise identifier
  name: {
    en: "string",                   // English name
    ar: "string"                    // Arabic name
  },
  description: {
    en: "string",                   // English description
    ar: "string"                    // Arabic description
  },
  instructions: {
    en: ["string"],                 // Step-by-step instructions
    ar: ["string"]
  },
  
  // Categorization
  category: "strength|cardio|flexibility|balance|sports",
  subcategory: "string",            // Specific subcategory
  muscleGroups: [                   // Primary muscle groups
    "chest|back|shoulders|arms|core|legs|glutes"
  ],
  equipment: [                      // Required equipment
    {
      name: "string",
      required: boolean,
      alternatives: ["string"]
    }
  ],
  
  // Difficulty and Metrics
  difficultyLevel: "beginner|intermediate|advanced",
  intensity: "low|moderate|high|very_high",
  estimatedCaloriesBurn: {          // Calories per minute
    beginner: number,
    intermediate: number,
    advanced: number
  },
  
  // Media Content
  media: {
    thumbnail: {
      url: "string",
      alt: "string"
    },
    images: [
      {
        url: "string",
        caption: "string",
        step: number
      }
    ],
    videos: [
      {
        muxPlaybackId: "string",
        duration: number,
        quality: "720p|1080p|4k",
        thumbnailUrl: "string"
      }
    ]
  },
  
  // Exercise Parameters
  defaultSets: number,
  defaultReps: number,
  defaultDuration: number,          // In seconds
  restTime: number,                 // Rest between sets
  
  // Variations and Progressions
  variations: [
    {
      name: "string",
      difficulty: "easier|harder",
      description: "string"
    }
  ],
  progressions: [
    {
      level: number,
      requirements: "string",
      modifications: "string"
    }
  ],
  
  // Safety and Contraindications
  safetyTips: {
    en: ["string"],
    ar: ["string"]
  },
  contraindications: [
    {
      condition: "string",
      severity: "avoid|modify|caution"
    }
  ],
  
  // Metadata
  createdBy: "string",              // Admin/coach who created
  createdAt: timestamp,
  updatedAt: timestamp,
  isActive: boolean,
  approvalStatus: "pending|approved|rejected",
  tags: ["string"],                 // Searchable tags
  
  // Analytics
  popularity: number,               // Usage frequency
  averageRating: number,            // User ratings
  totalRatings: number
}
```

### 4.3 Workout Plan Schema

```javascript
// Collection: workoutPlans/{planId}
{
  // Plan Identity
  id: "string",
  userId: "string",                 // Owner of the plan
  name: "string",
  description: "string",
  
  // Plan Configuration
  type: "strength|cardio|mixed|rehabilitation|sport_specific",
  duration: {
    weeks: number,
    sessionsPerWeek: number,
    sessionDuration: number         // Average minutes per session
  },
  difficultyLevel: "beginner|intermediate|advanced",
  
  // Target Goals
  primaryGoals: ["string"],         // Goal IDs
  targetMuscleGroups: ["string"],
  estimatedCaloriesBurn: number,    // Per session
  
  // Plan Structure
  weeks: [
    {
      weekNumber: number,
      focus: "string",              // Week focus/theme
      sessions: [
        {
          sessionNumber: number,
          name: "string",
          type: "strength|cardio|flexibility|rest",
          estimatedDuration: number,
          exercises: [
            {
              exerciseId: "string",
              order: number,
              sets: number,
              reps: number,
              duration: number,      // For time-based exercises
              weight: number,        // Suggested weight
              restTime: number,
              notes: "string",
              isSuperset: boolean,
              supersetGroup: number
            }
          ]
        }
      ]
    }
  ],
  
  // Personalization
  userModifications: [
    {
      exerciseId: "string",
      originalSets: number,
      modifiedSets: number,
      reason: "string",
      modifiedAt: timestamp
    }
  ],
  
  // Progress Tracking
  progressMetrics: [
    {
      metric: "weight|reps|duration|difficulty",
      targetIncrease: number,
      frequency: "weekly|biweekly|monthly"
    }
  ],
  
  // Generation Metadata
  generatedBy: "ai|coach|template",
  generationParameters: {
    userProfile: "object",          // Snapshot of user data
    preferences: "object",
    constraints: ["string"]
  },
  
  // Status and Lifecycle
  status: "draft|active|completed|paused|cancelled",
  startDate: timestamp,
  endDate: timestamp,
  completionRate: number,           // Percentage completed
  
  // System Fields
  createdAt: timestamp,
  updatedAt: timestamp,
  lastAccessedAt: timestamp,
  
  // Sharing and Collaboration
  isTemplate: boolean,
  isPublic: boolean,
  sharedWith: ["string"],           // User IDs
  coachId: "string",               // Assigned coach (if any)
  
  // Analytics
  totalSessions: number,
  completedSessions: number,
  averageSessionRating: number,
  userFeedback: "string"
}
```

### 4.4 User Progress Schema

```javascript
// Collection: userProgress/{userId}
{
  userId: "string",
  
  // Current Status
  currentWeight: number,
  currentBodyFat: number,           // If available
  lastMeasurementDate: timestamp,
  
  // Progress History
  weightHistory: [
    {
      value: number,
      date: timestamp,
      note: "string",
      source: "user_input|smart_scale|coach"
    }
  ],
  
  // Body Measurements
  measurements: {
    chest: {
      current: number,
      history: [
        {
          value: number,
          date: timestamp,
          unit: "cm|inches"
        }
      ]
    },
    waist: { /* same structure */ },
    hips: { /* same structure */ },
    biceps: { /* same structure */ },
    thighs: { /* same structure */ }
  },
  
  // Workout Progress
  workoutStats: {
    totalWorkouts: number,
    totalDuration: number,           // Total minutes exercised
    totalCaloriesBurned: number,
    averageWorkoutDuration: number,
    currentStreak: number,           // Days
    longestStreak: number,
    lastWorkoutDate: timestamp,
    
    // Strength Progress
    strengthMetrics: {
      benchPress: {
                current: number,
                unit: "kg|lbs",
                history: [
                  {
                    value: number,
                    date: timestamp,
                    reps: number
                  }
                ]
              },
      squat: { /* same structure */ },
      deadlift: { /* same structure */ }
      // Add more exercises as needed
    }
  },
  
  // Nutrition Progress
  nutritionStats: {
    averageDailyCalories: number,
    averageProtein: number,
    averageCarbs: number,
    averageFats: number,
    hydrationGoal: number,           // Daily water intake goal
    averageHydration: number,        // Average daily intake
    mealPlanAdherence: number        // Percentage
  },
  
  // Goal Progress
  goalProgress: [
    {
      goalId: "string",
      startDate: timestamp,
      targetDate: timestamp,
      startValue: number,
      targetValue: number,
      currentValue: number,
      progressPercentage: number,
      isCompleted: boolean,
      completedDate: timestamp
    }
  ],
  
  // Achievements
  achievements: [
    {
      achievementId: "string",
      unlockedDate: timestamp,
      category: "workout|nutrition|consistency|milestone",
      isNotified: boolean
    }
  ],
  
  // Health Metrics
  healthMetrics: {
    restingHeartRate: {
      current: number,
      history: [
        {
          value: number,
          date: timestamp,
          source: "manual|device"
        }
      ]
    },
    sleepQuality: {
      averageHours: number,
      averageQuality: number,        // 1-10 scale
      lastWeekAverage: number
    },
    stressLevel: {
      current: number,               // 1-10 scale
      weeklyAverage: number
    }
  },
  
  // System Fields
  createdAt: timestamp,
  updatedAt: timestamp,
  lastSyncAt: timestamp,
  
  // Data Sources
  dataSources: {
    weight: "manual|smart_scale|coach",
    measurements: "manual|coach",
    workouts: "app|external_device",
    nutrition: "app|external_app"
  }
}
```

### 4.5 Chat Schema

```javascript
// Collection: chats/{chatId}
{
  // Chat Identity
  id: "string",                     // Format: userId_coachId
  type: "user_coach|group|support",
  
  // Participants
  participants: [
    {
      userId: "string",
      role: "user|coach|admin",
      joinedAt: timestamp,
      isActive: boolean,
      lastReadAt: timestamp
    }
  ],
  
  // Chat Metadata
  title: "string",                  // Chat display name
  description: "string",
  isActive: boolean,
  
  // Last Message Info
  lastMessage: {
    messageId: "string",
    senderId: "string",
    content: "string",              // Preview text
    type: "text|image|video|file|system",
    timestamp: timestamp
  },
  
  // Unread Counts
  unreadCount: {
    "userId1": number,
    "userId2": number
  },
  
  // Chat Settings
  settings: {
    allowFileSharing: boolean,
    allowVoiceMessages: boolean,
    isEncrypted: boolean,
    retentionDays: number           // Message retention period
  },
  
  // System Fields
  createdAt: timestamp,
  updatedAt: timestamp,
  lastActivityAt: timestamp
}

// Subcollection: chats/{chatId}/messages/{messageId}
{
  // Message Identity
  id: "string",
  chatId: "string",
  senderId: "string",
  
  // Message Content
  type: "text|image|video|file|voice|system|workout_plan|diet_plan",
  content: "string",                // Text content
  
  // Media Content (if applicable)
  media: {
    url: "string",
    filename: "string",
    size: number,                   // File size in bytes
    mimeType: "string",
    duration: number,               // For audio/video
    thumbnail: "string"             // Thumbnail URL
  },
  
  // Rich Content (if applicable)
  richContent: {
    workoutPlanId: "string",
    dietPlanId: "string",
    exerciseId: "string",
    customData: "object"
  },
  
  // Message Status
  status: "sent|delivered|read|failed",
  isEdited: boolean,
  editedAt: timestamp,
  isDeleted: boolean,
  deletedAt: timestamp,
  
  // Reply Information
  replyTo: {
    messageId: "string",
    content: "string",              // Preview of replied message
    senderId: "string"
  },
  
  // Reactions
  reactions: {
    "👍": ["userId1", "userId2"],
    "❤️": ["userId3"],
    // Other emoji reactions
  },
  
  // System Fields
  timestamp: timestamp,
  serverTimestamp: timestamp,       // Server-side timestamp
  
  // Delivery Tracking
  deliveredTo: [
    {
      userId: "string",
      deliveredAt: timestamp
    }
  ],
  readBy: [
    {
      userId: "string",
      readAt: timestamp
    }
  ]
}
```

---

## 5. Relationships and References

### 5.1 Data Relationships

```mermaid
erDiagram
    USERS ||--o{ WORKOUT_PLANS : creates
    USERS ||--o{ USER_PROGRESS : tracks
    USERS ||--o{ SUBSCRIPTIONS : has
    USERS ||--o{ CHATS : participates
    
    COACHES ||--o{ USERS : coaches
    COACHES ||--o{ CHATS : participates
    
    WORKOUT_PLANS ||--o{ EXERCISES : contains
    WORKOUT_PLANS ||--o{ WORKOUT_SESSIONS : generates
    
    EXERCISES ||--o{ EXERCISE_LOGS : logged_in
    
    CHATS ||--o{ MESSAGES : contains
    
    USERS ||--o{ DIET_PLANS : has
    DIET_PLANS ||--o{ MEALS : contains
    MEALS ||--o{ FOODS : contains
```

### 5.2 Reference Patterns

#### Document References
```javascript
// Strong reference (document must exist)
{
  userId: "users/abc123",           // Full document path
  exerciseId: "exercises/push_up_001"
}

// Weak reference (document may not exist)
{
  coachId: "coaches/coach_001",     // Optional reference
  planId: "workoutPlans/plan_123"   // May be null
}
```

#### Subcollection References
```javascript
// Reference to subcollection document
{
  messageRef: "chats/chat_001/messages/msg_123",
  progressRef: "users/user_001/measurements/measurement_123"
}
```

---

## 6. Indexing Strategy

### 6.1 Composite Indexes

```javascript
// User queries
{
  collection: "users",
  fields: [
    { field: "subscriptionTier", order: "ASCENDING" },
    { field: "createdAt", order: "DESCENDING" }
  ]
}

// Exercise queries
{
  collection: "exercises",
  fields: [
    { field: "category", order: "ASCENDING" },
    { field: "difficultyLevel", order: "ASCENDING" },
    { field: "popularity", order: "DESCENDING" }
  ]
}

// Workout plan queries
{
  collection: "workoutPlans",
  fields: [
    { field: "userId", order: "ASCENDING" },
    { field: "status", order: "ASCENDING" },
    { field: "updatedAt", order: "DESCENDING" }
  ]
}

// Chat message queries
{
  collection: "chats/{chatId}/messages",
  fields: [
    { field: "timestamp", order: "DESCENDING" }
  ]
}
```

### 6.2 Single Field Indexes

```javascript
// Automatically created for:
- All document ID fields
- All fields used in where() clauses
- All fields used in orderBy() clauses

// Manually created for:
{
  collection: "users",
  field: "email",
  type: "ASCENDING"
},
{
  collection: "exercises",
  field: "tags",
  type: "ARRAY_CONTAINS"
}
```

---

## 7. Data Validation Rules

### 7.1 Client-Side Validation

```javascript
// User profile validation
const userProfileSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  age: Joi.number().integer().min(13).max(120).required(),
  gender: Joi.string().valid('male', 'female', 'other').required(),
  height: Joi.object({
    value: Joi.number().min(100).max(250).required(),
    unit: Joi.string().valid('cm', 'ft').required()
  }).required(),
  weight: Joi.object({
    current: Joi.number().min(30).max(300).required(),
    target: Joi.number().min(30).max(300).optional(),
    unit: Joi.string().valid('kg', 'lbs').required()
  }).required()
});

// Exercise validation
const exerciseSchema = Joi.object({
  name: Joi.object({
    en: Joi.string().min(3).max(100).required(),
    ar: Joi.string().min(3).max(100).required()
  }).required(),
  category: Joi.string().valid(
    'strength', 'cardio', 'flexibility', 'balance', 'sports'
  ).required(),
  difficultyLevel: Joi.string().valid(
    'beginner', 'intermediate', 'advanced'
  ).required(),
  defaultSets: Joi.number().integer().min(1).max(10).required(),
  defaultReps: Joi.number().integer().min(1).max(100).required()
});
```

### 7.2 Server-Side Validation (Cloud Functions)

```javascript
// Validate user data before saving
exports.validateUserData = functions.firestore
  .document('users/{userId}')
  .onWrite(async (change, context) => {
    const newData = change.after.data();
    const userId = context.params.userId;
    
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'age'];
    for (const field of requiredFields) {
      if (!newData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // Validate age range
    if (newData.age < 13 || newData.age > 120) {
      throw new Error('Age must be between 13 and 120');
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newData.email)) {
      throw new Error('Invalid email format');
    }
    
    return null;
  });
```

---

## 8. Security Rules

### 8.1 Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
      allow create: if request.auth != null 
        && request.auth.uid == userId
        && validateUserData(request.resource.data);
    }
    
    // User subcollections
    match /users/{userId}/{subcollection=**} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
    }
    
    // Exercises are read-only for users
    match /exercises/{exerciseId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && isAdmin(request.auth.uid);
    }
    
    // Workout plans
    match /workoutPlans/{planId} {
      allow read, write: if request.auth != null 
        && (resource.data.userId == request.auth.uid 
            || isCoach(request.auth.uid, resource.data.userId));
      allow create: if request.auth != null 
        && request.resource.data.userId == request.auth.uid;
    }
    
    // Chats - participants only
    match /chats/{chatId} {
      allow read, write: if request.auth != null 
        && request.auth.uid in resource.data.participants;
    }
    
    // Chat messages
    match /chats/{chatId}/messages/{messageId} {
      allow read: if request.auth != null 
        && isParticipant(request.auth.uid, chatId);
      allow create: if request.auth != null 
        && isParticipant(request.auth.uid, chatId)
        && request.resource.data.senderId == request.auth.uid;
      allow update: if request.auth != null 
        && resource.data.senderId == request.auth.uid;
    }
    
    // Admin-only collections
    match /adminSettings/{document} {
      allow read, write: if request.auth != null 
        && isAdmin(request.auth.uid);
    }
    
    // Helper functions
    function validateUserData(data) {
      return data.keys().hasAll(['firstName', 'lastName', 'email']) &&
             data.firstName is string &&
             data.lastName is string &&
             data.email is string &&
             data.age is number &&
             data.age >= 13 && data.age <= 120;
    }
    
    function isAdmin(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.role == 'admin';
    }
    
    function isCoach(coachId, userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.assignedCoach.coachId == coachId;
    }
    
    function isParticipant(userId, chatId) {
      return userId in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }
  }
}
```

---

## 9. Data Migration Strategy

### 9.1 Schema Evolution

#### Version Control
```javascript
// Document versioning
{
  schemaVersion: "1.2.0",
  data: {
    // Document data
  },
  migrations: [
    {
      fromVersion: "1.1.0",
      toVersion: "1.2.0",
      migratedAt: timestamp,
      changes: ["added_field_x", "removed_field_y"]
    }
  ]
}
```

#### Migration Functions
```javascript
// Cloud Function for data migration
exports.migrateUserProfiles = functions.https.onCall(async (data, context) => {
  const batch = admin.firestore().batch();
  const usersRef = admin.firestore().collection('users');
  
  const snapshot = await usersRef
    .where('schemaVersion', '<', '2.0.0')
    .limit(500)
    .get();
  
  snapshot.docs.forEach(doc => {
    const userData = doc.data();
    const migratedData = migrateUserDataToV2(userData);
    batch.update(doc.ref, migratedData);
  });
  
  await batch.commit();
  return { migratedCount: snapshot.size };
});

function migrateUserDataToV2(userData) {
  return {
    ...userData,
    schemaVersion: '2.0.0',
    // Add new fields with default values
    privacy: {
      profileVisibility: 'private',
      shareProgress: false,
      allowCoachContact: true
    },
    // Transform existing fields
    goals: userData.goals?.map(goal => ({
      goalId: goal,
      priority: 1,
      isActive: true,
      targetDate: null
    })) || []
  };
}
```

### 9.2 Backup Strategy

#### Automated Backups
```javascript
// Daily backup function
exports.dailyBackup = functions.pubsub
  .schedule('0 2 * * *')  // 2 AM daily
  .timeZone('UTC')
  .onRun(async (context) => {
    const timestamp = new Date().toISOString().split('T')[0];
    const backupName = `backup-${timestamp}`;
    
    const operation = await admin.firestore().exportDocuments({
      outputUriPrefix: `gs://do-it-backups/firestore/${backupName}`,
      collectionIds: [
        'users', 'exercises', 'workoutPlans', 'dietPlans',
        'chats', 'subscriptions', 'userProgress'
      ]
    });
    
    console.log(`Backup operation started: ${operation.name}`);
    return null;
  });
```

---

## 10. Performance Optimization

### 10.1 Query Optimization

#### Efficient Pagination
```javascript
// Cursor-based pagination
async function getWorkoutPlans(userId, lastDoc = null, limit = 10) {
  let query = firestore
    .collection('workoutPlans')
    .where('userId', '==', userId)
    .orderBy('updatedAt', 'desc')
    .limit(limit);
  
  if (lastDoc) {
    query = query.startAfter(lastDoc);
  }
  
  const snapshot = await query.get();
  return {
    docs: snapshot.docs,
    lastDoc: snapshot.docs[snapshot.docs.length - 1],
    hasMore: snapshot.docs.length === limit
  };
}
```

#### Denormalization for Performance
```javascript
// Denormalized user data in workout plans
{
  planId: "plan_123",
  userId: "user_456",
  // Denormalized user data for quick access
  userSnapshot: {
    name: "John Doe",
    fitnessLevel: "intermediate",
    goals: ["weight_loss", "muscle_gain"],
    lastUpdated: timestamp
  },
  // Plan data...
}
```

### 10.2 Caching Strategy

#### Client-Side Caching
```javascript
// Redux Toolkit Query with caching
const api = createApi({
  reducerPath: 'fitnessApi',
  baseQuery: firestoreBaseQuery,
  tagTypes: ['User', 'Exercise', 'WorkoutPlan'],
  endpoints: (builder) => ({
    getUser: builder.query({
      query: (userId) => ({ collection: 'users', doc: userId }),
      providesTags: ['User'],
      // Cache for 5 minutes
      keepUnusedDataFor: 300
    }),
    getExercises: builder.query({
      query: () => ({ collection: 'exercises' }),
      providesTags: ['Exercise'],
      // Cache for 1 hour
      keepUnusedDataFor: 3600
    })
  })
});
```

#### Server-Side Caching
```javascript
// Cloud Function with caching
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 }); // 10 minutes

exports.getPopularExercises = functions.https.onCall(async (data, context) => {
  const cacheKey = 'popular_exercises';
  let exercises = cache.get(cacheKey);
  
  if (!exercises) {
    const snapshot = await admin.firestore()
      .collection('exercises')
      .orderBy('popularity', 'desc')
      .limit(50)
      .get();
    
    exercises = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    cache.set(cacheKey, exercises);
  }
  
  return exercises;
});
```

### 10.3 Real-time Optimization

#### Selective Listening
```javascript
// Listen only to specific fields
const unsubscribe = firestore
  .collection('users')
  .doc(userId)
  .onSnapshot(
    { includeMetadataChanges: false },
    (doc) => {
      // Only update when actual data changes
      if (doc.exists && !doc.metadata.hasPendingWrites) {
        updateUserState(doc.data());
      }
    }
  );
```

#### Batch Operations
```javascript
// Batch writes for better performance
async function updateWorkoutProgress(userId, exercises) {
  const batch = firestore.batch();
  
  exercises.forEach(exercise => {
    const progressRef = firestore
      .collection('users')
      .doc(userId)
      .collection('workoutHistory')
      .doc();
    
    batch.set(progressRef, {
      exerciseId: exercise.id,
      sets: exercise.completedSets,
      reps: exercise.completedReps,
      weight: exercise.weight,
      completedAt: admin.firestore.FieldValue.serverTimestamp()
    });
  });
  
  // Update user stats
  const userRef = firestore.collection('users').doc(userId);
  batch.update(userRef, {
    'workoutStats.totalWorkouts': admin.firestore.FieldValue.increment(1),
    'workoutStats.lastWorkoutDate': admin.firestore.FieldValue.serverTimestamp()
  });
  
  await batch.commit();
}
```

---

**Navigation**: [← Back: UI/UX Wireframes](UI-UX-WIREFRAMES-AND-USER-FLOWS.MD) | [Next: API Specifications →](COMPREHENSIVE-API-SPECIFICATION.MD)
