# Do IT Application - API Specifications

## 9. API Specifications

### 9.1 Firebase Cloud Functions API

#### 9.1.1 User Management API

```javascript
// Generate Personalized Workout Plan
exports.generateWorkoutPlan = functions.https.onCall(async (data, context) => {
  // Input validation
  const { userId, preferences, fitnessLevel, goals } = data;
  
  // Authentication check
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    // [Implement AI-based plan generation logic here]
    const workoutPlan = await generatePlanWithAI(preferences, fitnessLevel, goals);
    
    // Save to Firestore
    await admin.firestore().collection('workoutPlans').add({
      ...workoutPlan,
      userId: userId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    return { success: true, planId: workoutPlan.id };
  } catch (error) {
    throw new functions.https.HttpsError('internal', 'Failed to generate workout plan');
  }
});
```

#### 9.1.2 Progress Tracking API

```javascript
// Calculate BMI and Calorie Requirements
exports.calculateMetrics = functions.https.onCall(async (data, context) => {
  const { height, weight, age, gender, activityLevel } = data;
  
  // Authentication check
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  // BMI Calculation
  const bmi = weight / Math.pow(height / 100, 2);
  
  // Calorie Calculation (Harris-Benedict Equation)
  let bmr;
  if (gender === 'male') {
    bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
  } else {
    bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
  }
  
  const activityMultipliers = {
    sedentary: 1.2,
    light: 1.375,
    moderate: 1.55,
    active: 1.725,
    veryActive: 1.9
  };
  
  const dailyCalories = bmr * activityMultipliers[activityLevel];
  
  // Update user's progress in Firestore
  await admin.firestore().collection('users').doc(context.auth.uid).update({
    'progress.bmi': Math.round(bmi * 10) / 10,
    'progress.dailyCalories': Math.round(dailyCalories),
    'progress.currentWeight': weight,
    'progress.lastUpdated': admin.firestore.FieldValue.serverTimestamp()
  });
  
  return {
    bmi: Math.round(bmi * 10) / 10,
    dailyCalories: Math.round(dailyCalories),
    bmr: Math.round(bmr)
  };
});
```

#### 9.1.3 Subscription Management API

```javascript
// Process Subscription Payment
exports.processSubscription = functions.https.onCall(async (data, context) => {
  const { planType, paymentToken } = data; // planType: "basic" or "vip"
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    const planPricing = {
      basic: { amount: 300, currency: 'QAR' },
      vip: { amount: 550, currency: 'QAR' }
    };
    
    const plan = planPricing[planType];
    if (!plan) {
      throw new functions.https.HttpsError('invalid-argument', 'Invalid plan type');
    }
    
    // [Integrate with payment gateway - Stripe/RevenueCat]
    const paymentResult = await processPayment(paymentToken, plan.amount, plan.currency);
    
    if (paymentResult.success) {
      // Update user subscription in Firestore
      const subscriptionData = {
        plan: planType,
        status: 'active',
        startDate: admin.firestore.FieldValue.serverTimestamp(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        amount: plan.amount,
        currency: plan.currency,
        autoRenew: true,
        paymentMethod: paymentResult.paymentMethod
      };
      
      await admin.firestore().collection('users').doc(context.auth.uid).update({
        subscription: subscriptionData
      });
      
      // Assign coach for VIP users
      if (planType === 'vip') {
        const availableCoach = await findAvailableCoach();
        if (availableCoach) {
          await admin.firestore().collection('users').doc(context.auth.uid).update({
            assignedCoach: availableCoach.id
          });
        }
      }
      
      return { success: true, subscriptionId: paymentResult.subscriptionId };
    }
    
    throw new functions.https.HttpsError('internal', 'Payment processing failed');
  } catch (error) {
    functions.logger.error('Subscription error:', error);
    throw new functions.https.HttpsError('internal', 'Subscription processing failed');
  }
});
```

#### 9.1.4 Mux Video Integration API

```javascript
// Upload Exercise Video to Mux
exports.uploadExerciseVideo = functions.https.onCall(async (data, context) => {
  const { exerciseId, videoFile } = data;
  
  // Check admin/coach permissions
  if (!context.auth || !hasRole(context.auth.uid, ['admin', 'coach'])) {
    throw new functions.https.HttpsError('permission-denied', 'Insufficient permissions');
  }
  
  try {
    // Create Mux asset
    const muxAsset = await mux.Video.Assets.create({
      input: videoFile.url,
      playback_policy: 'signed', // Secure playback
      mp4_support: 'standard'
    });
    
    // Update exercise document with Mux details
    await admin.firestore().collection('exercises').doc(exerciseId).update({
      muxAssetId: muxAsset.id,
      muxPlaybackId: muxAsset.playback_ids[0].id,
      videoURL: `https://stream.mux.com/${muxAsset.playback_ids[0].id}.m3u8`,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    return { 
      success: true, 
      assetId: muxAsset.id,
      playbackId: muxAsset.playback_ids[0].id 
    };
  } catch (error) {
    functions.logger.error('Mux upload error:', error);
    throw new functions.https.HttpsError('internal', 'Video upload failed');
  }
});
```

### 9.2 REST API Endpoints

| Endpoint | Method | Purpose | Request Body | Response |
|----------|--------|---------|--------------|----------|
| `/api/users/profile` | GET | Get user profile | - | User profile object |
| `/api/users/profile` | PUT | Update profile | Profile data | Updated profile |
| `/api/plans/workout` | POST | Generate workout plan | User preferences | Plan ID |
| `/api/plans/diet` | POST | Generate diet plan | Dietary preferences | Plan ID |
| `/api/progress` | POST | Log progress | Progress data | Success status |
| `/api/videos/upload` | POST | Upload video | Video file | Video URL |
| `/api/payments/process` | POST | Process payment | Payment details | Transaction ID |

### 9.3 Real-time APIs

#### 9.3.1 Chat System (VIP Users & Admin Communication)

```javascript
// Initialize Chat for VIP User
exports.initializeChat = functions.https.onCall(async (data, context) => {
  const { coachId } = data;
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  // Verify VIP subscription
  const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
  const userData = userDoc.data();
  
  if (!userData.subscription || userData.subscription.plan !== 'vip') {
    throw new functions.https.HttpsError('permission-denied', 'VIP subscription required');
  }
  
  const chatId = `${context.auth.uid}_${coachId}`;
  
  // Create chat document
  await admin.firestore().collection('chats').doc(chatId).set({
    participants: [context.auth.uid, coachId],
    userId: context.auth.uid,
    coachId: coachId,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    lastMessage: null,
    lastMessageTime: null,
    unreadCount: {
      [context.auth.uid]: 0,
      [coachId]: 0
    }
  });
  
  return { chatId };
});

// Send Message Function
exports.sendMessage = functions.https.onCall(async (data, context) => {
  const { chatId, message, messageType = 'text' } = data;
  
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    const messageData = {
      text: message,
      senderId: context.auth.uid,
      messageType: messageType, // 'text', 'image', 'video', 'file'
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      edited: false
    };
    
    // Add message to subcollection
    const messageRef = await admin.firestore()
      .collection('chats').doc(chatId)
      .collection('messages').add(messageData);
    
    // Update chat metadata
    await admin.firestore().collection('chats').doc(chatId).update({
      lastMessage: message,
      lastMessageTime: admin.firestore.FieldValue.serverTimestamp(),
      [`unreadCount.${getOtherParticipant(chatId, context.auth.uid)}`]: admin.firestore.FieldValue.increment(1)
    });
    
    return { messageId: messageRef.id };
  } catch (error) {
    functions.logger.error('Send message error:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send message');
  }
});

// Real-time Message Listener (Client-side)
const listenToMessages = (chatId, callback) => {
  return db.collection('chats').doc(chatId)
    .collection('messages')
    .orderBy('timestamp', 'asc')
    .onSnapshot((snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          callback(change.doc.data());
        }
      });
    });
};
```

#### 9.3.2 Push Notifications & Broadcasting

```javascript
// Send Push Notification
exports.sendPushNotification = functions.https.onCall(async (data, context) => {
  const { userIds, title, body, data: notificationData } = data;
  
  // Check admin permissions
  if (!context.auth || !hasRole(context.auth.uid, ['admin', 'coach'])) {
    throw new functions.https.HttpsError('permission-denied', 'Insufficient permissions');
  }
  
  try {
    const tokens = [];
    
    // Get FCM tokens for target users
    for (const userId of userIds) {
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      const userData = userDoc.data();
      if (userData.fcmToken) {
        tokens.push(userData.fcmToken);
      }
    }
    
    if (tokens.length === 0) {
      return { success: false, message: 'No valid tokens found' };
    }
    
    const message = {
      notification: { title, body },
      data: notificationData || {},
      tokens: tokens
    };
    
    const response = await admin.messaging().sendMulticast(message);
    
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    functions.logger.error('Push notification error:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// Broadcast to User Segments
exports.broadcastToSegment = functions.https.onCall(async (data, context) => {
  const { segment, title, body } = data; // segment: 'all', 'basic', 'vip', 'inactive'
  
  if (!context.auth || !hasRole(context.auth.uid, ['admin'])) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }
  
  let query = admin.firestore().collection('users');
  
  // Apply segment filters
  switch (segment) {
    case 'basic':
      query = query.where('subscription.plan', '==', 'basic');
      break;
    case 'vip':
      query = query.where('subscription.plan', '==', 'vip');
      break;
    case 'inactive':
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      query = query.where('lastLogin', '<', thirtyDaysAgo);
      break;
  }
  
  const users = await query.get();
  const tokens = users.docs
    .map(doc => doc.data().fcmToken)
    .filter(token => token);
  
  if (tokens.length === 0) {
    return { success: false, message: 'No users found in segment' };
  }
  
  const message = {
    notification: { title, body },
    tokens: tokens
  };
  
  const response = await admin.messaging().sendMulticast(message);
  
  return {
    success: true,
    targetUsers: users.size,
    successCount: response.successCount,
    failureCount: response.failureCount
  };
});
```

### 9.4 API Authentication & Security

#### 9.4.1 Authentication Flow
```javascript
// Client-side authentication example
import { signInWithEmailAndPassword, getIdToken } from 'firebase/auth';

const authenticateUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const idToken = await getIdToken(userCredential.user);
    
    // Use idToken for authenticated API calls
    return { success: true, token: idToken };
  } catch (error) {
    return { success: false, error: error.message };
  }
};
```

#### 9.4.2 API Rate Limiting
```javascript
// Rate limiting implementation
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Apply to all API routes
app.use('/api/', apiLimiter);
```

---

**Navigation**: [← Back: Data Architecture](05-DATA-ARCHITECTURE.MD) | [Back to Main](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: Security Architecture →](07-SECURITY-ARCHITECTURE.MD)