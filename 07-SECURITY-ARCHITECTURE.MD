# Do IT Application - Security Architecture

## 10. Security Architecture

### 10.1 Authentication & Authorization

#### 10.1.1 Security Flow Overview

```mermaid
graph TD
    A[User Login] --> B[Firebase Auth]
    B --> C{Authentication Success?}
    C -->|Yes| D[Generate JWT Token]
    C -->|No| E[Return Error]
    D --> F[Apply Security Rules]
    F --> G[Access Firestore]
    G --> H[Cloud Functions Authorization]
    H --> I[Data Encryption]
    I --> J[Secure Response]
```

#### 10.1.2 Firebase Authentication Implementation

```javascript
// Authentication service
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged 
} from 'firebase/auth';

class AuthService {
  constructor() {
    this.auth = getAuth();
    this.currentUser = null;
  }

  // User registration with validation
  async registerUser(email, password, userData) {
    try {
      // Input validation
      if (!this.validateEmail(email) || !this.validatePassword(password)) {
        throw new Error('Invalid email or password format');
      }

      const userCredential = await createUserWithEmailAndPassword(
        this.auth, 
        email, 
        password
      );
      
      // Create user profile in Firestore
      await this.createUserProfile(userCredential.user.uid, userData);
      
      return { success: true, user: userCredential.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Secure login with rate limiting
  async loginUser(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(
        this.auth, 
        email, 
        password
      );
      
      // Update last login timestamp
      await this.updateLastLogin(userCredential.user.uid);
      
      return { success: true, user: userCredential.user };
    } catch (error) {
      // Log failed login attempts
      await this.logFailedLogin(email);
      return { success: false, error: error.message };
    }
  }

  // Secure logout
  async logoutUser() {
    try {
      await signOut(this.auth);
      this.currentUser = null;
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Email validation
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Password strength validation
  validatePassword(password) {
    // Minimum 8 characters, at least one uppercase, lowercase, number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }
}
```

### 10.2 Firestore Security Rules

#### 10.2.1 User Data Protection

```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - strict access control
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
                     resource.data.role in ['admin', 'coach'] &&
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Exercises collection - read for authenticated users, write for admins/coaches
    match /exercises/{exerciseId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Workout Plans - user-specific access
    match /workoutPlans/{planId} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == resource.data.userId;
      allow read: if request.auth != null && 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Diet Plans - user-specific access
    match /dietPlans/{planId} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == resource.data.userId;
      allow read: if request.auth != null && 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Chats - VIP users and assigned coaches only
    match /chats/{chatId} {
      allow read, write: if request.auth != null && 
                            (request.auth.uid in resource.data.participants) &&
                            (isVIPUser(request.auth.uid) || isCoach(request.auth.uid));
      
      // Chat messages subcollection
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
                              request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      }
    }
    
    // Progress tracking - user-specific
    match /progress/{progressId} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == resource.data.userId;
      allow read: if request.auth != null && 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Notifications - user-specific
    match /notifications/{notificationId} {
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.userId;
      allow write: if request.auth != null && 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'coach'];
    }
    
    // Analytics - admin only
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && 
                            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Configuration - admin only
    match /config/{configId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Transactions - user-specific and admin access
    match /transactions/{transactionId} {
      allow read: if request.auth != null && 
                     (request.auth.uid == resource.data.userId || 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow write: if request.auth != null && 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper functions
    function isVIPUser(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.subscription.plan == 'vip';
    }
    
    function isCoach(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.role == 'coach';
    }
    
    function isAdmin(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.role == 'admin';
    }
  }
}
```

### 10.3 Data Protection & Encryption

#### 10.3.1 Data Encryption Strategy

```javascript
// Data encryption utilities
import CryptoJS from 'crypto-js';

class DataEncryption {
  constructor() {
    this.secretKey = process.env.ENCRYPTION_SECRET_KEY;
  }

  // Encrypt sensitive user data
  encryptSensitiveData(data) {
    try {
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(data), 
        this.secretKey
      ).toString();
      return encrypted;
    } catch (error) {
      throw new Error('Encryption failed');
    }
  }

  // Decrypt sensitive user data
  decryptSensitiveData(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.secretKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error('Decryption failed');
    }
  }

  // Hash passwords (additional layer)
  hashPassword(password) {
    return CryptoJS.SHA256(password).toString();
  }
}
```

#### 10.3.2 Secure File Storage

```javascript
// AWS S3 secure upload configuration
import AWS from 'aws-sdk';

class SecureFileStorage {
  constructor() {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION
    });
  }

  // Generate signed URL for secure uploads
  async generateSignedUploadUrl(fileName, fileType, userId) {
    const key = `users/${userId}/${Date.now()}-${fileName}`;
    
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
      ContentType: fileType,
      Expires: 300, // 5 minutes
      ACL: 'private'
    };

    try {
      const signedUrl = await this.s3.getSignedUrlPromise('putObject', params);
      return { signedUrl, key };
    } catch (error) {
      throw new Error('Failed to generate signed URL');
    }
  }

  // Generate signed URL for secure downloads
  async generateSignedDownloadUrl(key) {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
      Expires: 3600 // 1 hour
    };

    try {
      const signedUrl = await this.s3.getSignedUrlPromise('getObject', params);
      return signedUrl;
    } catch (error) {
      throw new Error('Failed to generate download URL');
    }
  }

  // Virus scanning integration
  async scanFileForViruses(key) {
    // Integration with AWS Lambda virus scanning
    // [Implement virus scanning logic]
    return { clean: true };
  }
}
```

### 10.4 API Security & Validation

#### 10.4.1 Input Validation & Sanitization

```javascript
// Input validation middleware
import Joi from 'joi';
import DOMPurify from 'dompurify';

class InputValidator {
  // User registration validation schema
  static userRegistrationSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])')).required(),
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required(),
    age: Joi.number().integer().min(13).max(120).required(),
    gender: Joi.string().valid('male', 'female', 'other').required(),
    height: Joi.number().min(100).max(250).required(),
    weight: Joi.number().min(30).max(300).required()
  });

  // Workout plan validation schema
  static workoutPlanSchema = Joi.object({
    name: Joi.string().min(3).max(100).required(),
    description: Joi.string().max(500),
    exercises: Joi.array().items(Joi.string()).min(1).required(),
    duration: Joi.number().integer().min(15).max(180).required(),
    difficulty: Joi.string().valid('beginner', 'intermediate', 'advanced').required()
  });

  // Sanitize HTML input
  static sanitizeHtml(input) {
    return DOMPurify.sanitize(input);
  }

  // Validate and sanitize user input
  static validateInput(data, schema) {
    const { error, value } = schema.validate(data);
    if (error) {
      throw new Error(`Validation error: ${error.details[0].message}`);
    }
    
    // Sanitize string fields
    const sanitized = {};
    for (const [key, val] of Object.entries(value)) {
      if (typeof val === 'string') {
        sanitized[key] = this.sanitizeHtml(val);
      } else {
        sanitized[key] = val;
      }
    }
    
    return sanitized;
  }
}
```

#### 10.4.2 Rate Limiting & DDoS Protection

```javascript
// Rate limiting implementation
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';

// General API rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: 15 * 60 // seconds
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Strict rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 15 * 60
  },
  skipSuccessfulRequests: true
});

// Speed limiting for heavy operations
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 10, // allow 10 requests per windowMs without delay
  delayMs: 500 // add 500ms delay per request after delayAfter
});

// Apply to routes
app.use('/api/', apiLimiter);
app.use('/api/auth/', authLimiter);
app.use('/api/upload/', speedLimiter);
```

### 10.5 Privacy Compliance

#### 10.5.1 GDPR Compliance Implementation

```javascript
// GDPR compliance utilities
class GDPRCompliance {
  // Data export for user requests
  async exportUserData(userId) {
    try {
      const userData = await admin.firestore().collection('users').doc(userId).get();
      const workoutPlans = await admin.firestore().collection('workoutPlans')
        .where('userId', '==', userId).get();
      const progress = await admin.firestore().collection('progress')
        .where('userId', '==', userId).get();
      
      const exportData = {
        profile: userData.data(),
        workoutPlans: workoutPlans.docs.map(doc => doc.data()),
        progress: progress.docs.map(doc => doc.data()),
        exportDate: new Date().toISOString()
      };
      
      return exportData;
    } catch (error) {
      throw new Error('Data export failed');
    }
  }

  // Data deletion for user requests
  async deleteUserData(userId) {
    const batch = admin.firestore().batch();
    
    try {
      // Delete user profile
      batch.delete(admin.firestore().collection('users').doc(userId));
      
      // Delete workout plans
      const workoutPlans = await admin.firestore().collection('workoutPlans')
        .where('userId', '==', userId).get();
      workoutPlans.docs.forEach(doc => batch.delete(doc.ref));
      
      // Delete progress records
      const progress = await admin.firestore().collection('progress')
        .where('userId', '==', userId).get();
      progress.docs.forEach(doc => batch.delete(doc.ref));
      
      // Delete chat messages
      const chats = await admin.firestore().collection('chats')
        .where('participants', 'array-contains', userId).get();
      chats.docs.forEach(doc => batch.delete(doc.ref));
      
      await batch.commit();
      
      // Log deletion for audit
      await this.logDataDeletion(userId);
      
      return { success: true };
    } catch (error) {
      throw new Error('Data deletion failed');
    }
  }

  // Audit logging
  async logDataDeletion(userId) {
    await admin.firestore().collection('auditLogs').add({
      action: 'data_deletion',
      userId: userId,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      reason: 'GDPR_request'
    });
  }
}
```

#### 10.5.2 Data Retention Policies

```javascript
// Automated data retention
exports.cleanupExpiredData = functions.pubsub.schedule('0 2 * * *').onRun(async (context) => {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const oneYearAgo = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
  
  try {
    // Delete expired sessions
    const expiredSessions = await admin.firestore().collection('sessions')
      .where('lastAccess', '<', thirtyDaysAgo).get();
    
    const batch = admin.firestore().batch();
    expiredSessions.docs.forEach(doc => batch.delete(doc.ref));
    
    // Archive old analytics data
    const oldAnalytics = await admin.firestore().collection('analytics')
      .where('timestamp', '<', oneYearAgo).get();
    
    oldAnalytics.docs.forEach(doc => {
      // Move to archive collection
      batch.set(admin.firestore().collection('analyticsArchive').doc(doc.id), doc.data());
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    
    functions.logger.info(`Cleaned up ${expiredSessions.size} expired sessions and ${oldAnalytics.size} old analytics records`);
  } catch (error) {
    functions.logger.error('Data cleanup failed:', error);
  }
});
```

### 10.6 Security Monitoring & Incident Response

#### 10.6.1 Security Event Logging

```javascript
// Security monitoring
class SecurityMonitor {
  static async logSecurityEvent(eventType, details, severity = 'medium') {
    const securityEvent = {
      type: eventType,
      details: details,
      severity: severity,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      resolved: false
    };
    
    await admin.firestore().collection('securityEvents').add(securityEvent);
    
    // Alert for high severity events
    if (severity === 'high') {
      await this.sendSecurityAlert(securityEvent);
    }
  }

  static async sendSecurityAlert(event) {
    // Send alert to security team
    // [Implement alerting mechanism]
  }

  // Monitor failed login attempts
  static async monitorFailedLogins(email, ip) {
    const recentAttempts = await admin.firestore().collection('failedLogins')
      .where('email', '==', email)
      .where('timestamp', '>', new Date(Date.now() - 15 * 60 * 1000))
      .get();
    
    if (recentAttempts.size >= 5) {
      await this.logSecurityEvent('brute_force_attempt', {
        email: email,
        ip: ip,
        attempts: recentAttempts.size
      }, 'high');
    }
  }
}
```

---

**Navigation**: [← Back: API Specifications](06-API-SPECIFICATIONS.MD) | [Back to Main](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: Deployment Architecture →](08-DEPLOYMENT-ARCHITECTURE.MD)