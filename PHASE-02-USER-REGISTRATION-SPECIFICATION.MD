# Phase 2: User Registration & Profile Creation - Detailed Specification

## Table of Contents

1. [Overview](#overview)
2. [Business Requirements](#business-requirements)
3. [Technical Architecture](#technical-architecture)
4. [User Interface Specifications](#user-interface-specifications)
5. [Data Models](#data-models)
6. [API Specifications](#api-specifications)
7. [Security Requirements](#security-requirements)
8. [Validation Rules](#validation-rules)
9. [Error Handling](#error-handling)
10. [Testing Requirements](#testing-requirements)

---

## 1. Overview

### 1.1 Feature Summary
The User Registration and Profile Creation feature enables new users to create accounts and set up comprehensive profiles that will be used for personalized fitness and nutrition plan generation.

### 1.2 User Journey
```mermaid
graph TD
    A[App Launch] --> B[Welcome Screen]
    B --> C[Language Selection]
    C --> D[Registration Screen]
    D --> E[Email Verification]
    E --> F[Profile Setup Screen]
    F --> G[Goal Selection]
    G --> H[Profile Complete]
    H --> I[Dashboard]
```

### 1.3 Success Criteria
- Users can successfully create accounts with email/password
- Profile data is securely stored in Firestore
- Registration flow has <5% abandonment rate
- Profile completion rate >90%
- Average registration time <3 minutes

---

## 2. Business Requirements

### 2.1 Functional Requirements

#### FR-001: User Account Creation
- **Description**: Users must be able to create accounts using email and password
- **Priority**: High
- **Acceptance Criteria**:
  - User enters valid email and password
  - System validates email format and password strength
  - Account is created in Firebase Authentication
  - User receives email verification
  - User is redirected to profile setup

#### FR-002: Profile Data Collection
- **Description**: System must collect comprehensive user profile data
- **Priority**: High
- **Acceptance Criteria**:
  - User enters personal information (name, age, gender)
  - User enters physical metrics (height, weight)
  - User selects fitness goals
  - All data is validated before submission
  - Profile is saved to Firestore

#### FR-003: Goal Selection
- **Description**: Users must select their primary fitness goals
- **Priority**: High
- **Acceptance Criteria**:
  - User can select from predefined goal options
  - Multiple goals can be selected
  - Goals influence plan generation algorithms
  - Goal preferences are saved to user profile

### 2.2 Non-Functional Requirements

#### NFR-001: Performance
- Registration screen loads within 2 seconds
- Profile data submission completes within 3 seconds
- Offline capability for form data entry

#### NFR-002: Usability
- Registration flow is intuitive and requires minimal instructions
- Form validation provides clear, actionable feedback
- Progress indicators show completion status

#### NFR-003: Security
- All data transmission is encrypted (HTTPS)
- Passwords meet security requirements
- User data is stored securely in Firestore
- Firestore security rules prevent unauthorized access

---

## 3. Technical Architecture

### 3.1 System Components

```mermaid
graph TB
    subgraph "Mobile App (React Native)"
        A[Registration Screen]
        B[Profile Setup Screen]
        C[Goal Selection Screen]
    end
    
    subgraph "Firebase Services"
        D[Firebase Auth]
        E[Firestore Database]
        F[Cloud Functions]
    end
    
    subgraph "State Management"
        G[Redux Store]
        H[Auth Slice]
        I[User Slice]
    end
    
    A --> D
    B --> E
    C --> E
    A --> G
    B --> G
    C --> G
    D --> F
    E --> F
```

### 3.2 Technology Stack
- **Frontend**: React Native 0.72+
- **Navigation**: React Navigation 6
- **State Management**: Redux Toolkit
- **UI Components**: React Native Elements
- **Authentication**: Firebase Authentication
- **Database**: Firestore
- **Validation**: Joi validation library

### 3.3 Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant RS as Registration Screen
    participant FA as Firebase Auth
    participant PS as Profile Setup Screen
    participant FS as Firestore
    participant CF as Cloud Functions
    
    U->>RS: Enter email/password
    RS->>FA: createUserWithEmailAndPassword()
    FA->>RS: User credential
    RS->>PS: Navigate to profile setup
    U->>PS: Enter profile data
    PS->>FS: Save user profile
    FS->>CF: Trigger profile creation
    CF->>FS: Initialize user data
    PS->>U: Registration complete
```

---

## 4. User Interface Specifications

### 4.1 Registration Screen

#### 4.1.1 Layout Structure
```
┌─────────────────────────────────┐
│           App Logo              │
│                                 │
│        Welcome to Do IT         │
│     Create your account         │
│                                 │
│  ┌─────────────────────────────┐│
│  │ Email Address               ││
│  │ [email input field]         ││
│  └─────────────────────────────┘│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Password                    ││
│  │ [password input field]      ││
│  └─────────────────────────────┘│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Confirm Password            ││
│  │ [password input field]      ││
│  └─────────────────────────────┘│
│                                 │
│  [Create Account Button]        │
│                                 │
│  Already have an account?       │
│  [Sign In Link]                 │
└─────────────────────────────────┘
```

#### 4.1.2 Component Specifications
- **Email Input**: 
  - Type: Email keyboard
  - Validation: Real-time email format validation
  - Error states: Invalid format, already exists
- **Password Input**: 
  - Type: Secure text entry
  - Validation: Minimum 8 characters, mixed case, numbers, symbols
  - Strength indicator: Visual password strength meter
- **Create Account Button**: 
  - State: Enabled only when all fields are valid
  - Loading state: Shows spinner during account creation

### 4.2 Profile Setup Screen

#### 4.2.1 Layout Structure
```
┌─────────────────────────────────┐
│     Complete Your Profile       │
│                                 │
│  ┌─────────────────────────────┐│
│  │ First Name                  ││
│  │ [text input]                ││
│  └─────────────────────────────┘│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Last Name                   ││
│  │ [text input]                ││
│  └─────────────────────────────┘│
│                                 │
│  Gender: [Male] [Female] [Other]│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Age                         ││
│  │ [number input]              ││
│  └─────────────────────────────┘│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Height (cm)                 ││
│  │ [number input]              ││
│  └─────────────────────────────┘│
│                                 │
│  ┌─────────────────────────────┐│
│  │ Weight (kg)                 ││
│  │ [number input]              ││
│  └─────────────────────────────┘│
│                                 │
│  [Continue Button]              │
└─────────────────────────────────┘
```

#### 4.2.2 Component Specifications
- **Name Fields**: Text inputs with character limits (2-50 characters)
- **Gender Selection**: Radio button group with three options
- **Age Input**: Number input with range validation (13-120)
- **Height Input**: Number input with unit selector (cm/ft)
- **Weight Input**: Number input with unit selector (kg/lbs)

---

## 5. Data Models

### 5.1 User Authentication Model
```javascript
// Firebase Authentication User Object
{
  uid: "string",           // Unique user identifier
  email: "string",         // User email address
  emailVerified: boolean,  // Email verification status
  createdAt: timestamp,    // Account creation time
  lastSignInTime: timestamp // Last login time
}
```

### 5.2 User Profile Model
```javascript
// Firestore users/{userId} document
{
  // Personal Information
  uid: "string",                    // Firebase Auth UID
  email: "string",                  // User email
  firstName: "string",              // First name (2-50 chars)
  lastName: "string",               // Last name (2-50 chars)
  gender: "male|female|other",      // Gender selection
  age: number,                      // Age (13-120)
  
  // Physical Metrics
  height: {
    value: number,                  // Height value
    unit: "cm|ft"                  // Height unit
  },
  weight: {
    current: number,                // Current weight
    target: number,                 // Target weight (optional)
    unit: "kg|lbs"                 // Weight unit
  },
  
  // Fitness Goals
  goals: [string],                  // Array of goal IDs
  activityLevel: "sedentary|light|moderate|active|very_active",
  
  // System Fields
  createdAt: timestamp,             // Profile creation time
  updatedAt: timestamp,             // Last update time
  profileComplete: boolean,         // Profile completion status
  subscriptionTier: "free|basic|vip", // Subscription level
  
  // Preferences
  language: "en|ar",               // Language preference
  units: {
    weight: "kg|lbs",
    height: "cm|ft",
    distance: "km|miles"
  }
}
```

### 5.3 Fitness Goals Model
```javascript
// Firestore fitnessGoals/{goalId} document
{
  id: "string",                    // Goal identifier
  name: {
    en: "string",                  // English name
    ar: "string"                   // Arabic name
  },
  description: {
    en: "string",                  // English description
    ar: "string"                   // Arabic description
  },
  category: "weight_loss|muscle_gain|endurance|strength|flexibility",
  icon: "string",                  // Icon identifier
  isActive: boolean,               // Goal availability
  sortOrder: number                // Display order
}
```

---

## 6. API Specifications

### 6.1 Firebase Authentication APIs

#### 6.1.1 Create User Account
```javascript
// Function: createUserWithEmailAndPassword
// Input:
{
  email: "<EMAIL>",
  password: "SecurePass123!"
}

// Success Response:
{
  user: {
    uid: "abc123",
    email: "<EMAIL>",
    emailVerified: false
  }
}

// Error Response:
{
  code: "auth/email-already-in-use",
  message: "The email address is already in use by another account."
}
```

### 6.2 Firestore APIs

#### 6.2.1 Create User Profile
```javascript
// Collection: users
// Document ID: {userId}
// Method: set()

// Input Data:
{
  uid: "abc123",
  email: "<EMAIL>",
  firstName: "John",
  lastName: "Doe",
  gender: "male",
  age: 25,
  height: { value: 175, unit: "cm" },
  weight: { current: 70, unit: "kg" },
  goals: ["weight_loss", "muscle_gain"],
  activityLevel: "moderate",
  language: "en",
  profileComplete: true,
  createdAt: FieldValue.serverTimestamp(),
  updatedAt: FieldValue.serverTimestamp()
}
```

### 6.3 Cloud Functions

#### 6.3.1 Initialize User Data
```javascript
// Function: initializeUserData
// Trigger: Firestore onCreate users/{userId}

exports.initializeUserData = functions.firestore
  .document('users/{userId}')
  .onCreate(async (snap, context) => {
    const userData = snap.data();
    const userId = context.params.userId;
    
    // Initialize user-specific collections
    await Promise.all([
      // Create user progress tracking
      firestore.collection('userProgress').doc(userId).set({
        userId: userId,
        currentWeight: userData.weight.current,
        measurements: {},
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }),
      
      // Create user preferences
      firestore.collection('userPreferences').doc(userId).set({
        userId: userId,
        notifications: {
          workoutReminders: true,
          mealReminders: true,
          progressUpdates: true
        },
        privacy: {
          profileVisibility: 'private',
          shareProgress: false
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      })
    ]);
    
    return null;
  });
```

---

## 7. Security Requirements

### 7.1 Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
      allow create: if request.auth != null 
        && request.auth.uid == userId
        && validateUserData(request.resource.data);
    }
    
    // Fitness goals are read-only for all authenticated users
    match /fitnessGoals/{goalId} {
      allow read: if request.auth != null;
      allow write: if false; // Only admins can modify goals
    }
    
    // User progress is private to each user
    match /userProgress/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
    }
    
    // User preferences are private to each user
    match /userPreferences/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
    }
  }
  
  // Validation function for user data
  function validateUserData(data) {
    return data.keys().hasAll(['uid', 'email', 'firstName', 'lastName']) &&
           data.uid is string &&
           data.email is string &&
           data.firstName is string &&
           data.lastName is string &&
           data.age is number &&
           data.age >= 13 && data.age <= 120;
  }
}
```

### 7.2 Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (!@#$%^&*)
- Maximum 128 characters

### 7.3 Data Encryption
- All data transmission uses HTTPS/TLS 1.3
- Firestore data is encrypted at rest
- Sensitive data fields are hashed where appropriate
- User passwords are handled by Firebase Authentication

---

## 8. Validation Rules

### 8.1 Client-Side Validation

#### 8.1.1 Email Validation
```javascript
const emailSchema = Joi.string()
  .email({ tlds: { allow: false } })
  .required()
  .messages({
    'string.email': 'Please enter a valid email address',
    'any.required': 'Email is required'
  });
```

#### 8.1.2 Password Validation
```javascript
const passwordSchema = Joi.string()
  .min(8)
  .max(128)
  .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
  .required()
  .messages({
    'string.min': 'Password must be at least 8 characters',
    'string.max': 'Password cannot exceed 128 characters',
    'string.pattern.base': 'Password must contain uppercase, lowercase, number, and special character',
    'any.required': 'Password is required'
  });
```

#### 8.1.3 Profile Data Validation
```javascript
const profileSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  gender: Joi.string().valid('male', 'female', 'other').required(),
  age: Joi.number().integer().min(13).max(120).required(),
  height: Joi.object({
    value: Joi.number().min(100).max(250).required(),
    unit: Joi.string().valid('cm', 'ft').required()
  }).required(),
  weight: Joi.object({
    current: Joi.number().min(30).max(300).required(),
    target: Joi.number().min(30).max(300).optional(),
    unit: Joi.string().valid('kg', 'lbs').required()
  }).required(),
  goals: Joi.array().items(Joi.string()).min(1).required(),
  activityLevel: Joi.string().valid('sedentary', 'light', 'moderate', 'active', 'very_active').required()
});
```

---

## 9. Error Handling

### 9.1 Authentication Errors

| Error Code | Message | User Action |
|------------|---------|-------------|
| `auth/email-already-in-use` | This email is already registered | Try signing in or use a different email |
| `auth/invalid-email` | Please enter a valid email address | Correct the email format |
| `auth/weak-password` | Password is too weak | Use a stronger password |
| `auth/network-request-failed` | Network error occurred | Check internet connection and try again |

### 9.2 Validation Errors

| Field | Error Type | Message |
|-------|------------|---------|
| Email | Invalid format | Please enter a valid email address |
| Password | Too weak | Password must contain uppercase, lowercase, number, and special character |
| First Name | Too short | First name must be at least 2 characters |
| Age | Out of range | Age must be between 13 and 120 |
| Height | Invalid value | Height must be between 100-250 cm |
| Weight | Invalid value | Weight must be between 30-300 kg |

### 9.3 Error Display Strategy
- **Inline Validation**: Show errors immediately below input fields
- **Form Validation**: Validate entire form on submit
- **Toast Messages**: Show success/error messages for API calls
- **Retry Mechanisms**: Automatic retry for network failures

---

## 10. Testing Requirements

### 10.1 Unit Tests
- Input validation functions
- Redux action creators and reducers
- Firebase service functions
- Utility functions

### 10.2 Integration Tests
- Firebase Authentication flow
- Firestore data operations
- Navigation between screens
- Form submission workflows

### 10.3 End-to-End Tests
- Complete registration flow
- Profile creation and update
- Error handling scenarios
- Cross-platform compatibility

### 10.4 Test Coverage Requirements
- Unit tests: 90% coverage
- Integration tests: 80% coverage
- E2E tests: Critical user paths
- Performance tests: Load time benchmarks

---

**Navigation**: [← Back: Phase 1](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: Phase 3 →](PHASE-03-FEATURE-SPECIFICATIONS.MD)
