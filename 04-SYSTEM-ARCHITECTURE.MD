# Do IT Application - System Architecture

## 5. System Context Diagram

```mermaid
graph TB
    User[End Users<br/>Mobile App] 
    Coach[Coaches<br/>Mobile App]
    Admin[Administrators<br/>Web Dashboard]
    
    DoIT[Do IT Application<br/>System]
    
    Firebase[Firebase<br/>Backend Services]
    AWS[AWS S3<br/>File Storage]
    Mux[Mux<br/>Video Platform]
    Payment[Payment<br/>Gateway]
    AI[Google AI<br/>Platform]
    
    User --> DoIT
    Coach --> DoIT
    Admin --> DoIT
    
    DoIT --> Firebase
    DoIT --> AWS
    DoIT --> Mux
    DoIT --> Payment
    DoIT --> AI
    
    style DoIT fill:#e1f5fe
    style Firebase fill:#fff3e0
    style AWS fill:#f3e5f5
    style Mux fill:#e8f5e8
```

### 5.1 External Systems
- **Payment Gateway**: [Specify payment provider - Stripe/PayPal/etc.]
- **Push Notification Service**: Firebase Cloud Messaging
- **Email Service**: [Specify email provider - SendGrid/AWS SES/etc.]
- **Analytics Platform**: Firebase Analytics, Google Analytics

---

## 6. Container Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        Mobile[React Native<br/>Mobile App<br/>iOS & Android]
        Web[Next.js<br/>Admin Dashboard<br/>Web Browser]
    end
    
    subgraph "Firebase Backend"
        Auth[Firebase<br/>Authentication]
        Firestore[Firestore<br/>Database]
        Functions[Cloud Functions<br/>Business Logic]
        Storage[Firebase<br/>Storage]
    end
    
    subgraph "External Services"
        S3[AWS S3<br/>File Storage]
        MuxAPI[Mux API<br/>Video Platform]
        AI[Google AI<br/>Platform]
    end
    
    Mobile --> Auth
    Mobile --> Firestore
    Mobile --> Functions
    Web --> Auth
    Web --> Firestore
    Web --> Functions
    
    Functions --> S3
    Functions --> MuxAPI
    Functions --> AI
    Functions --> Firestore
    
    style Mobile fill:#e3f2fd
    style Web fill:#e8f5e8
    style Auth fill:#fff3e0
    style Firestore fill:#fff3e0
    style Functions fill:#fff3e0
```

### 6.1 Container Responsibilities

#### 6.1.1 React Native Mobile App
- **Purpose**: Primary user interface for end-users and coaches
- **Responsibilities**:
  - User authentication and profile management
  - Workout and diet plan display
  - Video content playback
  - Progress tracking and data input
  - Real-time messaging with coaches
  - Push notification handling

#### 6.1.2 Next.js Admin Dashboard
- **Purpose**: Web-based administrative interface
- **Responsibilities**:
  - User management and analytics
  - Content management (videos, plans, articles)
  - Coach management and assignment
  - Payment and subscription management
  - System monitoring and reporting

#### 6.1.3 Firebase Backend
- **Purpose**: Core backend services and data management
- **Responsibilities**:
  - User authentication and authorization
  - Data persistence and real-time synchronization
  - Business logic execution
  - API endpoint management
  - Integration with external services

---

## 7. Component Architecture

### 7.1 React Native Mobile App Components

```mermaid
graph TB
    subgraph "React Native App"
        Auth[Authentication<br/>Components]
        Profile[User Profile<br/>Management]
        Workout[Workout Plan<br/>Display]
        Video[Video Player<br/>Component]
        Progress[Progress Tracking<br/>Components]
        Chat[Messaging<br/>Components]
        Navigation[Navigation<br/>System]
    end
    
    subgraph "Shared Services"
        API[API Service<br/>Layer]
        State[State Management<br/>Redux Store]
        Storage[Local Storage<br/>AsyncStorage]
    end
    
    Auth --> API
    Profile --> API
    Workout --> API
    Video --> API
    Progress --> API
    Chat --> API
    
    API --> State
    State --> Storage
```

#### 7.1.1 Key Components

| Component | Description | Key Features |
|-----------|-------------|--------------|
| **Authentication** | Login/signup flows | Social login, biometric auth, password reset |
| **User Profile** | Profile management | Personal info, preferences, goals |
| **Workout Plans** | Exercise routines | Plan display, progress tracking, timer |
| **Video Player** | Exercise videos | Streaming, offline download, playback controls |
| **Progress Tracking** | User metrics | Weight, measurements, photo progress |
| **Messaging** | Coach communication | Real-time chat, file sharing, notifications |

### 7.2 Next.js Admin Dashboard Components

```mermaid
graph TB
    subgraph "Next.js Admin Dashboard"
        Dashboard[Admin<br/>Dashboard]
        UserMgmt[User<br/>Management]
        ContentMgmt[Content<br/>Management]
        CoachMgmt[Coach<br/>Management]
        Analytics[Analytics<br/>& Reports]
        Settings[System<br/>Settings]
    end
    
    subgraph "Shared Components"
        Layout[Layout<br/>Components]
        Forms[Form<br/>Components]
        Tables[Data Table<br/>Components]
        Charts[Chart<br/>Components]
    end
    
    Dashboard --> Layout
    UserMgmt --> Forms
    UserMgmt --> Tables
    ContentMgmt --> Forms
    CoachMgmt --> Tables
    Analytics --> Charts
```

### 7.3 Firebase Cloud Functions Components

```mermaid
graph TB
    subgraph "Cloud Functions"
        UserFunc[User Management<br/>Functions]
        PlanFunc[Plan Generation<br/>Functions]
        PaymentFunc[Payment<br/>Processing]
        NotifFunc[Notification<br/>Functions]
        AIFunc[AI Integration<br/>Functions]
        DataFunc[Data Processing<br/>Functions]
    end
    
    subgraph "External Integrations"
        MuxInt[Mux API<br/>Integration]
        S3Int[AWS S3<br/>Integration]
        AIInt[Google AI<br/>Integration]
        PayInt[Payment Gateway<br/>Integration]
    end
    
    PlanFunc --> AIInt
    PaymentFunc --> PayInt
    DataFunc --> S3Int
    UserFunc --> MuxInt
```

---

**Navigation**: [← Back: Architecture Principles](03-ARCHITECTURE-PRINCIPLES.MD) | [Back to Main](PHASE-01-PROJECT-FOUNDATION.MD) | [Next: Data Architecture →](05-DATA-ARCHITECTURE.MD)