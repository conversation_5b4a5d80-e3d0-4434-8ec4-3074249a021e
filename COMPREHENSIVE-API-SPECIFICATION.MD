# Comprehensive API Specification

## Table of Contents

1. [Overview](#overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Firebase Cloud Functions](#firebase-cloud-functions)
4. [Firestore Database Operations](#firestore-database-operations)
5. [Real-time Subscriptions](#real-time-subscriptions)
6. [File Upload & Media Management](#file-upload--media-management)
7. [Third-Party Integrations](#third-party-integrations)
8. [Error Handling](#error-handling)
9. [Rate Limiting & Security](#rate-limiting--security)
10. [API Testing & Documentation](#api-testing--documentation)

---

## 1. Overview

### 1.1 API Architecture
The Do IT application uses a **serverless API architecture** built on Firebase Cloud Functions, providing scalable, event-driven backend services.

### 1.2 API Design Principles
- **RESTful Design**: Consistent HTTP methods and status codes
- **Stateless Operations**: Each request contains all necessary information
- **Idempotent Operations**: Safe to retry failed requests
- **Versioning**: API versioning for backward compatibility
- **Security First**: Authentication and authorization on all endpoints

### 1.3 Base URLs
```
Production:  https://us-central1-do-it-fitness.cloudfunctions.net/
Development: https://us-central1-do-it-fitness-dev.cloudfunctions.net/
Local:       http://localhost:5001/do-it-fitness-dev/us-central1/
```

### 1.4 API Response Format
```javascript
// Success Response
{
  "success": true,
  "data": {
    // Response data
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_abc123"
}

// Error Response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_abc123"
}
```

---

## 2. Authentication & Authorization

### 2.1 Firebase Authentication

#### 2.1.1 User Registration
```javascript
// Endpoint: createUserAccount (Callable Function)
// Method: POST
// Authentication: None (public endpoint)

// Request
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "userData": {
    "firstName": "John",
    "lastName": "Doe",
    "language": "en"
  }
}

// Response
{
  "success": true,
  "data": {
    "uid": "abc123def456",
    "email": "<EMAIL>",
    "emailVerified": false,
    "customToken": "eyJhbGciOiJSUzI1NiJ9...",
    "profile": {
      "firstName": "John",
      "lastName": "Doe",
      "profileComplete": false
    }
  }
}

// Error Responses
{
  "success": false,
  "error": {
    "code": "EMAIL_ALREADY_EXISTS",
    "message": "The email address is already in use by another account"
  }
}
```

#### 2.1.2 User Login
```javascript
// Endpoint: authenticateUser (Callable Function)
// Method: POST
// Authentication: None (public endpoint)

// Request
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}

// Response
{
  "success": true,
  "data": {
    "uid": "abc123def456",
    "email": "<EMAIL>",
    "emailVerified": true,
    "customToken": "eyJhbGciOiJSUzI1NiJ9...",
    "profile": {
      "firstName": "John",
      "lastName": "Doe",
      "subscriptionTier": "basic",
      "profileComplete": true
    },
    "lastLoginAt": "2024-01-15T10:30:00Z"
  }
}
```

#### 2.1.3 Password Reset
```javascript
// Endpoint: resetPassword (Callable Function)
// Method: POST
// Authentication: None (public endpoint)

// Request
{
  "email": "<EMAIL>"
}

// Response
{
  "success": true,
  "data": {
    "message": "Password reset email sent successfully"
  }
}
```

### 2.2 Authorization Middleware

#### 2.2.1 Token Validation
```javascript
// Middleware function for all protected endpoints
async function validateAuthToken(req, res, next) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Authorization token is required'
        }
      });
    }
    
    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      }
    });
  }
}
```

#### 2.2.2 Role-Based Access Control
```javascript
// Role validation middleware
function requireRole(roles) {
  return async (req, res, next) => {
    try {
      const userDoc = await admin.firestore()
        .collection('users')
        .doc(req.user.uid)
        .get();
      
      const userData = userDoc.data();
      const userRole = userData?.role || 'user';
      
      if (!roles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions for this operation'
          }
        });
      }
      
      req.userRole = userRole;
      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: 'Error validating user permissions'
        }
      });
    }
  };
}
```

---

## 3. Firebase Cloud Functions

### 3.1 User Management Functions

#### 3.1.1 Complete User Profile
```javascript
// Endpoint: completeUserProfile (Callable Function)
// Method: POST
// Authentication: Required
// Authorization: User can only update their own profile

// Request
{
  "profileData": {
    "firstName": "John",
    "lastName": "Doe",
    "gender": "male",
    "age": 25,
    "height": {
      "value": 175,
      "unit": "cm"
    },
    "weight": {
      "current": 70,
      "target": 65,
      "unit": "kg"
    },
    "goals": ["weight_loss", "muscle_gain"],
    "activityLevel": "moderate",
    "medicalConditions": [],
    "preferences": {
      "language": "en",
      "units": {
        "weight": "kg",
        "height": "cm"
      }
    }
  }
}

// Response
{
  "success": true,
  "data": {
    "profileComplete": true,
    "userId": "abc123def456",
    "bmi": 22.9,
    "recommendedCalories": 2200,
    "fitnessLevel": "intermediate",
    "initialPlanGenerated": true
  }
}

// Implementation
exports.completeUserProfile = functions.https.onCall(async (data, context) => {
  // Validate authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const userId = context.auth.uid;
  const { profileData } = data;
  
  try {
    // Validate input data
    const validationResult = validateProfileData(profileData);
    if (!validationResult.isValid) {
      throw new functions.https.HttpsError('invalid-argument', validationResult.error);
    }
    
    // Calculate derived metrics
    const bmi = calculateBMI(profileData.height, profileData.weight.current);
    const recommendedCalories = calculateDailyCalories(profileData);
    const fitnessLevel = determineFitnessLevel(profileData);
    
    // Prepare user document
    const userDoc = {
      ...profileData,
      uid: userId,
      bmi: bmi,
      recommendedCalories: recommendedCalories,
      fitnessLevel: fitnessLevel,
      profileComplete: true,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };
    
    // Save to Firestore
    await admin.firestore().collection('users').doc(userId).update(userDoc);
    
    // Trigger initial plan generation
    await generateInitialPlans(userId, profileData);
    
    return {
      profileComplete: true,
      userId: userId,
      bmi: bmi,
      recommendedCalories: recommendedCalories,
      fitnessLevel: fitnessLevel,
      initialPlanGenerated: true
    };
    
  } catch (error) {
    console.error('Error completing user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to complete profile');
  }
});
```

#### 3.1.2 Update User Progress
```javascript
// Endpoint: updateUserProgress (Callable Function)
// Method: POST
// Authentication: Required

// Request
{
  "progressData": {
    "weight": {
      "value": 68,
      "date": "2024-01-15T10:30:00Z",
      "note": "After morning workout"
    },
    "measurements": {
      "waist": {
        "value": 85,
        "unit": "cm",
        "date": "2024-01-15T10:30:00Z"
      }
    },
    "workoutCompleted": {
      "planId": "plan_123",
      "sessionId": "session_456",
      "exercises": [
        {
          "exerciseId": "exercise_789",
          "sets": 3,
          "reps": 12,
          "weight": 50,
          "duration": 180,
          "completed": true
        }
      ],
      "totalDuration": 45,
      "caloriesBurned": 320,
      "difficulty": "moderate",
      "notes": "Felt strong today"
    }
  }
}

// Response
{
  "success": true,
  "data": {
    "progressUpdated": true,
    "newBMI": 22.1,
    "weightChange": -2.0,
    "streakDays": 5,
    "achievementsUnlocked": [
      {
        "id": "weight_loss_5kg",
        "name": "5kg Weight Loss",
        "description": "Lost 5kg from starting weight"
      }
    ],
    "nextMilestone": {
      "type": "weight",
      "target": 65,
      "remaining": 3,
      "unit": "kg"
    }
  }
}
```

---

**Navigation**: [← Back: Enhanced Data Architecture](ENHANCED-DATA-ARCHITECTURE.MD) | [Next: Security Architecture →](ENHANCED-SECURITY-ARCHITECTURE.MD)
